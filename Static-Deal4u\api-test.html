<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test - Deal4u</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .loading { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 Deal4u API Connection Test</h1>
    
    <div class="test-container">
        <h2>Configuration Check</h2>
        <div id="config-status"></div>
    </div>

    <div class="test-container">
        <h2>API Connection Test</h2>
        <button onclick="testConnection()">Test WooCommerce API</button>
        <div id="connection-status"></div>
    </div>

    <div class="test-container">
        <h2>Products Test</h2>
        <button onclick="testProducts()">Load Sample Products</button>
        <div id="products-status"></div>
        <div id="products-list"></div>
    </div>

    <script>
        // Environment configuration
        window.ENV = {
            NEXT_PUBLIC_APP_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WORDPRESS_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WOOCOMMERCE_URL: 'https://deal4u.co',
            WOOCOMMERCE_CONSUMER_KEY: 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193',
            WOOCOMMERCE_CONSUMER_SECRET: 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3'
        };

        // Check configuration on load
        document.addEventListener('DOMContentLoaded', function() {
            checkConfiguration();
        });

        function checkConfiguration() {
            const configDiv = document.getElementById('config-status');
            const config = window.ENV;
            
            let html = '<h3>Current Configuration:</h3>';
            html += `<div class="info">Base URL: ${config.NEXT_PUBLIC_WOOCOMMERCE_URL}</div>`;
            html += `<div class="info">Consumer Key: ${config.WOOCOMMERCE_CONSUMER_KEY ? '✅ Set' : '❌ Missing'}</div>`;
            html += `<div class="info">Consumer Secret: ${config.WOOCOMMERCE_CONSUMER_SECRET ? '✅ Set' : '❌ Missing'}</div>`;
            
            configDiv.innerHTML = html;
        }

        async function testConnection() {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.innerHTML = '<div class="loading">🔄 Testing connection...</div>';

            try {
                const baseURL = window.ENV.NEXT_PUBLIC_WOOCOMMERCE_URL;
                const consumerKey = window.ENV.WOOCOMMERCE_CONSUMER_KEY;
                const consumerSecret = window.ENV.WOOCOMMERCE_CONSUMER_SECRET;
                
                const credentials = btoa(`${consumerKey}:${consumerSecret}`);
                const url = `${baseURL}/wp-json/wc/v3/system_status`;

                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Basic ${credentials}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    statusDiv.innerHTML = `
                        <div class="success">✅ Connection successful!</div>
                        <div class="info">WooCommerce Version: ${data.environment?.version || 'Unknown'}</div>
                        <div class="info">WordPress Version: ${data.environment?.wp_version || 'Unknown'}</div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ Connection failed</div>
                        <div class="error">Status: ${response.status} ${response.statusText}</div>
                        <div class="info">This might be due to CORS restrictions or incorrect API keys</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error">❌ Connection error: ${error.message}</div>
                    <div class="info">This is likely due to CORS restrictions when testing locally</div>
                `;
            }
        }

        async function testProducts() {
            const statusDiv = document.getElementById('products-status');
            const listDiv = document.getElementById('products-list');
            
            statusDiv.innerHTML = '<div class="loading">🔄 Loading products...</div>';
            listDiv.innerHTML = '';

            try {
                const baseURL = window.ENV.NEXT_PUBLIC_WOOCOMMERCE_URL;
                const consumerKey = window.ENV.WOOCOMMERCE_CONSUMER_KEY;
                const consumerSecret = window.ENV.WOOCOMMERCE_CONSUMER_SECRET;
                
                const credentials = btoa(`${consumerKey}:${consumerSecret}`);
                const url = `${baseURL}/wp-json/wc/v3/products?per_page=5&status=publish`;

                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Basic ${credentials}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const products = await response.json();
                    statusDiv.innerHTML = `<div class="success">✅ Loaded ${products.length} products</div>`;
                    
                    if (products.length > 0) {
                        let html = '<h3>Sample Products:</h3>';
                        products.forEach(product => {
                            html += `
                                <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;">
                                    <strong>${product.name}</strong><br>
                                    Price: £${product.price}<br>
                                    Status: ${product.status}<br>
                                    ${product.images && product.images.length > 0 ? 
                                        `<img src="${product.images[0].src}" alt="${product.name}" style="max-width: 100px; height: auto; margin-top: 5px;">` 
                                        : 'No image'}
                                </div>
                            `;
                        });
                        listDiv.innerHTML = html;
                    }
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ Failed to load products</div>
                        <div class="error">Status: ${response.status} ${response.statusText}</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error">❌ Error loading products: ${error.message}</div>
                `;
            }
        }
    </script>
</body>
</html>
