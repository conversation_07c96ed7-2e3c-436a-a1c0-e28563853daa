// Shop Page Specific Functionality
class ShopManager extends ProductsManager {
    constructor() {
        super();
        this.currentView = 'grid';
        this.currentSort = 'default';
        this.currentPriceFilter = null;
        this.initShop();
    }

    initShop() {
        this.bindShopEvents();
        this.loadShopProducts();
        this.updateProductCount();
    }

    bindShopEvents() {
        // View toggle
        $$('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.closest('.view-btn').dataset.view;
                this.toggleView(view);
            });
        });

        // Sort dropdown
        const sortSelect = $('#sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortProducts(e.target.value);
            });
        }

        // Price filters
        $$('.filter-btn[data-price]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const priceRange = e.target.dataset.price;
                this.filterByPrice(priceRange);
            });
        });

        // Clear filters
        const clearBtn = $('.clear-filters');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearAllFilters();
            });
        }
    }

    async loadShopProducts() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        showLoading('#shop-products-grid', 'Loading all products...');

        try {
            this.products = await loadProducts();
            this.filteredProducts = [...this.products];
            this.renderShopProducts();
            this.updateProductCount();
            this.updateLoadMoreButton();
        } catch (error) {
            console.error('Error loading shop products:', error);
            this.showError('Failed to load products. Please try again later.');
        } finally {
            this.isLoading = false;
        }
    }

    renderShopProducts() {
        const container = $('#shop-products-grid');
        if (!container) return;

        if (this.filteredProducts.length === 0) {
            container.innerHTML = `
                <div class="no-products">
                    <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                    <h3>No products found</h3>
                    <p>Try adjusting your filters or search criteria</p>
                    <button class="btn-primary" onclick="shopManager.clearAllFilters()">Clear Filters</button>
                </div>
            `;
            return;
        }

        const productsToShow = this.filteredProducts.slice(0, this.currentPage * this.productsPerPage);
        
        if (this.currentView === 'grid') {
            container.className = 'products-grid';
            container.innerHTML = productsToShow.map(product => this.createProductCard(product)).join('');
        } else {
            container.className = 'products-list';
            container.innerHTML = productsToShow.map(product => this.createProductListItem(product)).join('');
        }
        
        // Add click events to product cards
        $$('.product-card, .product-list-item').forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't open modal if clicking on add to cart button
                if (e.target.closest('.add-to-cart')) return;
                
                const productId = parseInt(card.dataset.id);
                this.openProductModal(productId);
            });
        });
    }

    createProductListItem(product) {
        const discount = calculateDiscount(product.regular_price, product.price);
        const category = getCategoryByKeywords(product.name, product.short_description);
        const categoryInfo = CONFIG.CATEGORIES[category];
        
        return `
            <div class="product-list-item" data-id="${product.id}">
                <div class="product-list-image">
                    <img src="${getImageUrl(product)}" alt="${product.name}" loading="lazy">
                    ${discount > 0 ? `<div class="product-badge">${discount}% OFF</div>` : ''}
                </div>
                
                <div class="product-list-info">
                    <div class="product-list-header">
                        <h3 class="product-title">${product.name}</h3>
                        <div class="product-price">
                            <span class="current-price">${formatPrice(product.price)}</span>
                            ${product.regular_price && product.regular_price !== product.price ? 
                                `<span class="original-price">${formatPrice(product.regular_price)}</span>` : ''}
                        </div>
                    </div>
                    
                    <div class="product-rating">
                        <div class="stars">${'★'.repeat(5)}</div>
                        <span class="rating-text">(${Math.floor(Math.random() * 100) + 50} reviews)</span>
                    </div>
                    
                    <div class="product-description">
                        ${truncate(stripHtml(product.short_description || product.description || ''), 150)}
                    </div>
                    
                    <div class="product-features">
                        <span class="feature-tag">
                            <i class="${categoryInfo.icon}"></i>
                            ${categoryInfo.name}
                        </span>
                        ${product.featured ? '<span class="feature-tag">Featured</span>' : ''}
                        ${product.on_sale ? '<span class="feature-tag">Sale</span>' : ''}
                    </div>
                </div>
                
                <div class="product-list-actions">
                    <button class="add-to-cart" onclick="addToCart(${JSON.stringify(product).replace(/"/g, '&quot;')}, 1); event.stopPropagation();">
                        <i class="fas fa-shopping-cart"></i>
                        Add to Cart
                    </button>
                    <button class="quick-view" onclick="shopManager.openProductModal(${product.id}); event.stopPropagation();">
                        <i class="fas fa-eye"></i>
                        Quick View
                    </button>
                </div>
            </div>
        `;
    }

    toggleView(view) {
        if (this.currentView === view) return;

        this.currentView = view;
        
        // Update active button
        $$('.view-btn').forEach(btn => {
            removeClass(btn, 'active');
            if (btn.dataset.view === view) {
                addClass(btn, 'active');
            }
        });

        this.renderShopProducts();
    }

    sortProducts(sortBy) {
        this.currentSort = sortBy;
        
        switch (sortBy) {
            case 'price-low':
                this.filteredProducts.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
                break;
            case 'price-high':
                this.filteredProducts.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
                break;
            case 'name':
                this.filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'newest':
                this.filteredProducts.sort((a, b) => new Date(b.date_created) - new Date(a.date_created));
                break;
            case 'featured':
                this.filteredProducts.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
                break;
            default:
                // Default sorting (restore original order)
                this.filteredProducts = this.products.filter(product => 
                    this.filteredProducts.some(filtered => filtered.id === product.id)
                );
                break;
        }
        
        this.currentPage = 1;
        this.renderShopProducts();
        this.updateLoadMoreButton();
    }

    filterByPrice(priceRange) {
        // Update active filter button
        $$('.filter-btn[data-price]').forEach(btn => {
            removeClass(btn, 'active');
            if (btn.dataset.price === priceRange) {
                addClass(btn, 'active');
            }
        });

        this.currentPriceFilter = priceRange;
        this.applyAllFilters();
    }

    applyAllFilters() {
        let filtered = [...this.products];

        // Apply category filter
        if (this.currentCategory && this.currentCategory !== 'all') {
            filtered = filtered.filter(product => {
                const productCategory = getCategoryByKeywords(
                    product.name, 
                    product.short_description || product.description || ''
                );
                return productCategory === this.currentCategory;
            });
        }

        // Apply price filter
        if (this.currentPriceFilter) {
            filtered = filtered.filter(product => {
                const price = parseFloat(product.price);
                switch (this.currentPriceFilter) {
                    case '0-25':
                        return price >= 0 && price <= 25;
                    case '25-50':
                        return price > 25 && price <= 50;
                    case '50-100':
                        return price > 50 && price <= 100;
                    case '100+':
                        return price > 100;
                    default:
                        return true;
                }
            });
        }

        this.filteredProducts = filtered;
        this.currentPage = 1;
        this.sortProducts(this.currentSort);
        this.updateProductCount();
    }

    clearAllFilters() {
        // Reset all filters
        this.currentCategory = 'all';
        this.currentPriceFilter = null;
        this.currentSort = 'default';
        
        // Update UI
        $$('.filter-btn').forEach(btn => {
            removeClass(btn, 'active');
            if (btn.dataset.category === 'all') {
                addClass(btn, 'active');
            }
        });
        
        const sortSelect = $('#sort-select');
        if (sortSelect) sortSelect.value = 'default';
        
        // Reapply filters (which will show all products)
        this.applyAllFilters();
        
        showToast('All filters cleared', 'info');
    }

    updateProductCount() {
        const totalProducts = $('#total-products');
        if (totalProducts) {
            totalProducts.textContent = this.filteredProducts.length;
        }
        
        // Update products header
        const productsHeader = $('.products-header h2');
        if (productsHeader) {
            productsHeader.textContent = `All Products (${this.filteredProducts.length})`;
        }
    }

    async filterByCategory(category) {
        await super.filterByCategory(category);
        this.applyAllFilters();
    }

    async searchProducts(query) {
        await super.searchProducts(query);
        this.updateProductCount();
    }
}

// Initialize shop manager when DOM is ready
let shopManager;

document.addEventListener('DOMContentLoaded', () => {
    shopManager = new ShopManager();
});

// Global functions for shop page
function clearAllFilters() {
    if (shopManager) {
        shopManager.clearAllFilters();
    }
}

// Add shop-specific styles
const shopStyles = `
<style>
.shop-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.shop-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.shop-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.shop-hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.shop-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.shop-filters {
    background: #f8fafc;
    padding: 2rem 0;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.clear-filters {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
}

.clear-filters:hover {
    background: #dc2626;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.filter-group h3 {
    margin-bottom: 1rem;
    color: #1f2937;
}

.price-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.sort-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    background: #f3f4f6;
    border: 2px solid #e5e7eb;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
}

.view-btn.active,
.view-btn:hover {
    background: #2563eb;
    color: white;
    border-color: #2563eb;
}

.products-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.product-list-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    cursor: pointer;
    border: 1px solid #e5e7eb;
}

.product-list-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.product-list-image {
    width: 200px;
    height: 150px;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.product-list-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-list-info {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-list-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.product-list-actions {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    border-left: 1px solid #e5e7eb;
    min-width: 150px;
}

.quick-view {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 600;
    width: 100%;
}

.quick-view:hover {
    background: #667eea;
    color: white;
}

@media (max-width: 768px) {
    .shop-hero h1 {
        font-size: 2rem;
    }
    
    .shop-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .filters-grid {
        grid-template-columns: 1fr;
    }
    
    .products-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .product-list-item {
        flex-direction: column;
    }
    
    .product-list-image {
        width: 100%;
        height: 200px;
    }
    
    .product-list-actions {
        border-left: none;
        border-top: 1px solid #e5e7eb;
        flex-direction: row;
        min-width: auto;
    }
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', shopStyles);
