<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deal4u - Amazing Deals on Premium Products</title>

    <!-- Preconnect to external domains for better performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://images.unsplash.com">

    <!-- Favicon and app icons -->
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="manifest" href="manifest.json">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Deal4u">
    <meta name="application-name" content="Deal4u">
    <meta name="msapplication-TileColor" content="#3b82f6">
    <meta name="msapplication-tooltip" content="Deal4u - Amazing Deals">
    <meta name="msapplication-starturl" content="/">
    <meta name="msapplication-navbutton-color" content="#3b82f6">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.">
    <meta name="keywords" content="ecommerce, deals, shopping, products, online store">
    <meta name="author" content="Deal4u Team">

    <!-- Open Graph -->
    <meta property="og:type" content="website">
    <meta property="og:locale" content="en_US">
    <meta property="og:site_name" content="Deal4u">
    <meta property="og:title" content="Deal4u - Amazing Deals on Premium Products">
    <meta property="og:description" content="Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.">
    <meta property="og:image" content="/og-image.jpg">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@deal4u">
    <meta name="twitter:creator" content="@deal4u">
    <meta name="twitter:title" content="Deal4u - Amazing Deals on Premium Products">
    <meta name="twitter:description" content="Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.">
    <meta name="twitter:image" content="/og-image.jpg">

    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'spin-slow': 'spin 3s linear infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'pulse-slow': 'pulse 3s infinite',
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">

    <!-- Prevent FOUC -->
    <script>
        try {
            const mode = localStorage.getItem('theme');
            if (mode && mode === 'dark') {
                document.documentElement.classList.add('dark');
            }
        } catch (e) {
            console.error('Error accessing localStorage:', e);
        }
    </script>
</head>
<body class="antialiased min-h-screen flex flex-col bg-gray-50">
    <!-- Summer Sale Banner - Above Hero Section -->
    <div class="bg-yellow-400 text-purple-900 py-4 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center justify-between gap-4">
                <div class="flex items-center gap-3">
                    <div class="bg-purple-900 text-yellow-400 text-xl font-extrabold p-2 rounded-lg rotate-3 shadow-lg">
                        50% OFF
                    </div>
                    <div>
                        <h3 class="text-lg md:text-xl font-bold">SUMMER MEGA SALE</h3>
                        <p class="text-sm">Limited time offer on all products!</p>
                    </div>
                </div>
                <a href="shop.html?sale=true" class="bg-purple-900 hover:bg-purple-800 text-white font-bold py-2 px-6 rounded-lg shadow-md transition-colors">
                    Shop Now
                </a>
            </div>
        </div>
        <!-- Decorative elements -->
        <div class="absolute -right-8 top-0 bottom-0 w-32 bg-purple-900 opacity-20 rotate-12"></div>
        <div class="absolute left-1/3 top-0 bottom-0 w-8 bg-purple-900 opacity-10 -rotate-12"></div>
    </div>

    <!-- Header -->
    <header id="header" class="sticky top-0 z-50 transition-all duration-300 bg-white shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <a href="/" class="flex items-center space-x-2 flex-shrink-0">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-lg font-bold text-xl hover:from-blue-700 hover:to-purple-700 transition-colors">
                        Deal4u
                    </div>
                </a>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-1">
                    <a href="/" class="px-3 py-2 rounded-md text-sm font-medium text-blue-600 bg-blue-50 transition-colors">Home</a>
                    <a href="shop.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Shop</a>
                    <a href="categories.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Categories</a>
                    <a href="track-order.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Track Order</a>
                    <a href="about.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">About</a>
                    <a href="contact.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Contact</a>
                    <a href="faq.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">FAQ</a>
                </nav>

                <!-- Search Bar - Desktop -->
                <div class="hidden md:flex flex-1 max-w-lg mx-8">
                    <form onsubmit="handleSearchSubmit(event)" class="relative w-full">
                        <div class="relative">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                            <input
                                type="text"
                                placeholder="Search products..."
                                id="search-input"
                                oninput="handleSearchInput(this)"
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                            />
                            <button
                                type="button"
                                onclick="openImageSearch()"
                                class="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600 transition-colors"
                                title="Search by image"
                            >
                                <i data-lucide="camera" class="w-5 h-5"></i>
                            </button>
                            <input
                                type="file"
                                accept="image/*"
                                class="hidden"
                                id="image-search-input"
                                onchange="handleImageUpload(event)"
                            />
                            <button
                                type="submit"
                                id="search-submit-btn"
                                class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 hidden"
                            >
                                Search
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Right Side Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Wishlist - Desktop -->
                    <a href="wishlist.html" class="hidden md:flex items-center space-x-1 text-gray-700 hover:text-red-500 transition-colors">
                        <i data-lucide="heart" class="w-5 h-5"></i>
                        <span class="text-sm">Wishlist</span>
                    </a>

                    <!-- Shopping Cart -->
                    <a href="cart.html" class="relative flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors">
                        <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                        <span class="hidden md:block text-sm">Cart</span>
                        <span id="cart-count" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                    </a>

                    <!-- User Menu -->
                    <div class="relative">
                        <button
                            id="user-menu-btn"
                            onclick="toggleUserMenu()"
                            class="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                            <i data-lucide="user" class="w-5 h-5"></i>
                            <span class="hidden md:block text-sm" id="user-menu-text">Login</span>
                        </button>

                        <!-- User Dropdown -->
                        <div id="user-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50 hidden">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <button
                        class="md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors"
                        onclick="toggleMobileMenu()"
                        id="mobile-menu-btn"
                    >
                        <i data-lucide="menu" class="w-6 h-6" id="mobile-menu-icon"></i>
                    </button>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="md:hidden bg-white border-t border-gray-200 py-4 hidden">
                <!-- Mobile Search -->
                <div class="px-2 pb-4">
                    <form onsubmit="handleSearchSubmit(event)" class="relative">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                        <input
                            type="text"
                            placeholder="Search products..."
                            id="mobile-search-input"
                            oninput="handleSearchInput(this)"
                            class="w-full pl-10 pr-16 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <button
                            type="button"
                            onclick="openImageSearch()"
                            class="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600 transition-colors"
                            title="Search by image"
                        >
                            <i data-lucide="camera" class="w-5 h-5"></i>
                        </button>
                        <button
                            type="submit"
                            id="mobile-search-submit-btn"
                            class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 hidden"
                        >
                            Search
                        </button>
                    </form>
                </div>

                <!-- Mobile Navigation -->
                <div class="space-y-1">
                    <a href="/" class="block px-3 py-2 rounded-md text-base font-medium text-blue-600 bg-blue-50 transition-colors">Home</a>
                    <a href="shop.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Shop</a>
                    <a href="categories.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Categories</a>
                    <a href="track-order.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Track Order</a>
                    <a href="about.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">About</a>
                    <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Contact</a>
                    <a href="faq.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">FAQ</a>
                    <a href="wishlist.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-red-500 hover:bg-gray-50 transition-colors">Wishlist</a>
                    <a href="register.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Register</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">

        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E'); background-size: 30px 30px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24 relative z-10">
            <!-- Promotional Badge - Floating -->
            <div class="absolute top-4 right-4 md:top-8 md:right-8 animate-bounce-slow">
                <div class="relative w-24 h-24 md:w-32 md:h-32 bg-yellow-400 rounded-full flex items-center justify-center transform rotate-12 shadow-xl">
                    <div class="text-center px-2">
                        <p class="text-xl md:text-2xl font-black text-purple-900 leading-none">50%</p>
                        <p class="text-sm md:text-base font-bold text-purple-900 leading-none">OFF</p>
                        <p class="text-[10px] md:text-xs mt-1 font-semibold text-purple-800">SUMMER50</p>
                    </div>
                </div>
            </div>

            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-2/3 text-center md:text-left">
                    <div class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full mb-6 animate-pulse">
                        <span class="text-xs font-semibold tracking-wider">🔥 SUMMER SALE NOW LIVE 🔥</span>
                    </div>

                    <h1 class="text-4xl md:text-6xl font-bold mb-6">
                        Premium Products,
                        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
                            Amazing Deals
                        </span>
                    </h1>
                    <p class="text-xl mb-8 max-w-2xl">
                        Discover thousands of high-quality products at unbeatable prices. Fast shipping, competitive deals, and exceptional customer service - that's the Deal4u promise.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 mb-8 md:mb-0">
                        <a
                            href="shop.html?sale=true"
                            class="bg-yellow-400 text-purple-900 px-8 py-3 rounded-lg font-bold hover:bg-yellow-300 transition-colors flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                        >
                            <span>Shop Summer Sale</span>
                            <i data-lucide="arrow-right" class="w-5 h-5"></i>
                        </a>
                        <a
                            href="shop.html"
                            class="bg-white bg-opacity-20 backdrop-blur-sm text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-700 transition-colors border border-white border-opacity-40"
                        >
                            Browse All
                        </a>
                    </div>
                </div>

                <!-- Image/Visual Element -->
                <div class="md:w-1/3 hidden md:block relative">
                    <div class="relative z-0 mt-8">
                        <div class="absolute -inset-4 bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500 rounded-full opacity-50 blur-xl animate-pulse-slow"></div>
                        <div class="relative bg-white bg-opacity-20 backdrop-blur-md rounded-2xl p-4 shadow-2xl border border-white border-opacity-20">
                            <!-- Dashed border on the card -->
                            <div class="absolute -inset-3 rounded-full border-2 border-dashed border-yellow-600/90 animate-spin-slow" style="z-index: 30;"></div>
                            <div class="aspect-square w-full max-w-[300px] flex flex-col items-center justify-between text-center p-6">
                                <!-- Top section -->
                                <div class="text-5xl font-black mt-2">SALE</div>

                                <!-- Middle section - main offer -->
                                <div class="flex flex-col items-center -mt-4">
                                    <div class="text-xl font-bold mb-1">Up to</div>
                                    <div class="text-7xl font-extrabold text-yellow-400 leading-none drop-shadow-lg">50%</div>
                                    <div class="text-2xl font-bold tracking-wider mt-1">OFF</div>
                                    <div class="h-px w-3/4 bg-white opacity-30 my-2"></div>
                                </div>

                                <!-- Bottom section -->
                                <div class="text-sm font-medium py-1 px-4 bg-white bg-opacity-20 rounded-full shadow-inner">Limited Time Only</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trust Indicators -->
            <div class="mt-12 flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
                <div class="flex items-center space-x-2">
                    <div class="flex text-yellow-400">
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                    </div>
                    <span class="text-blue-100">4.9/5 from 10,000+ reviews</span>
                </div>
                <div class="text-blue-100">
                    📦 Free shipping on orders £50+
                </div>
                <div class="text-blue-100">
                    🔒 Secure checkout guaranteed
                </div>
            </div>
        </div>

        <!-- Wave Decoration -->
        <div class="absolute bottom-0 left-0 right-0">
            <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full h-auto">
                <path d="M0 120L48 105C96 90 192 60 288 55C384 50 480 70 576 75C672 80 768 70 864 65C960 60 1056 60 1152 65C1248 70 1344 80 1392 85L1440 90V120H1392C1344 120 1248 120 1152 120C1056 120 960 120 864 120C768 120 672 120 576 120C480 120 384 120 288 120C192 120 96 120 48 120H0Z" fill="white" fill-opacity="0.05"/>
                <path d="M0 120L48 110C96 100 192 80 288 75C384 70 480 80 576 85C672 90 768 90 864 85C960 80 1056 70 1152 75C1248 80 1344 100 1392 110L1440 120V120H1392C1344 120 1248 120 1152 120C1056 120 960 120 864 120C768 120 672 120 576 120C480 120 384 120 288 120C192 120 96 120 48 120H0Z" fill="white" fill-opacity="0.1"/>
            </svg>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose Deal4u?</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    We provide the best shopping experience with premium products at amazing deals and exceptional service.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 bg-blue-100 text-blue-600">
                        <i data-lucide="truck" class="w-8 h-8"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-gray-900">Fast Shipping</h3>
                    <p class="text-gray-600">Free shipping on orders over $50 with 2-day delivery available.</p>
                </div>

                <div class="text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 bg-green-100 text-green-600">
                        <i data-lucide="shield" class="w-8 h-8"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-gray-900">Secure Payment</h3>
                    <p class="text-gray-600">256-bit SSL encryption ensures your payment information is safe.</p>
                </div>

                <div class="text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 bg-purple-100 text-purple-600">
                        <i data-lucide="refresh-cw" class="w-8 h-8"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-gray-900">Easy Returns</h3>
                    <p class="text-gray-600">30-day return policy with hassle-free returns and exchanges.</p>
                </div>

                <div class="text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 bg-orange-100 text-orange-600">
                        <i data-lucide="award" class="w-8 h-8"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-gray-900">Quality Guarantee</h3>
                    <p class="text-gray-600">All products are thoroughly tested and come with quality guarantee.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">
                    Trusted by Thousands Worldwide
                </h2>
                <p class="text-lg text-gray-600">
                    Join our growing community of satisfied customers
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div class="bg-gradient-to-br from-blue-50 to-purple-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-blue-100 group-hover:to-purple-100 transition-colors">
                        <i data-lucide="users" class="w-10 h-10 text-blue-600"></i>
                    </div>
                    <div class="text-4xl font-bold text-gray-900 mb-2" id="stat-customers">50,000+</div>
                    <div class="text-lg font-semibold text-gray-700 mb-1">Happy Customers</div>
                    <p class="text-sm text-gray-500">Satisfied customers worldwide</p>
                </div>

                <div class="text-center group">
                    <div class="bg-gradient-to-br from-blue-50 to-purple-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-blue-100 group-hover:to-purple-100 transition-colors">
                        <i data-lucide="shopping-bag" class="w-10 h-10 text-blue-600"></i>
                    </div>
                    <div class="text-4xl font-bold text-gray-900 mb-2" id="stat-products">1M+</div>
                    <div class="text-lg font-semibold text-gray-700 mb-1">Products Sold</div>
                    <p class="text-sm text-gray-500">Products delivered successfully</p>
                </div>

                <div class="text-center group">
                    <div class="bg-gradient-to-br from-blue-50 to-purple-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-blue-100 group-hover:to-purple-100 transition-colors">
                        <i data-lucide="globe" class="w-10 h-10 text-blue-600"></i>
                    </div>
                    <div class="text-4xl font-bold text-gray-900 mb-2" id="stat-countries">25+</div>
                    <div class="text-lg font-semibold text-gray-700 mb-1">Countries Served</div>
                    <p class="text-sm text-gray-500">Countries with fast delivery</p>
                </div>

                <div class="text-center group">
                    <div class="bg-gradient-to-br from-blue-50 to-purple-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-blue-100 group-hover:to-purple-100 transition-colors">
                        <i data-lucide="trending-up" class="w-10 h-10 text-blue-600"></i>
                    </div>
                    <div class="text-4xl font-bold text-gray-900 mb-2" id="stat-experience">5+</div>
                    <div class="text-lg font-semibold text-gray-700 mb-1">Years Experience</div>
                    <p class="text-sm text-gray-500">Years of e-commerce excellence</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Products</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Discover our hand-picked selection of premium products
                </p>
            </div>

            <div id="featured-products-container">
                <div class="text-center py-12" id="products-loading">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-4 text-gray-600">Loading amazing deals from your WooCommerce store...</p>
                </div>

                <div id="featured-products-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 hidden">
                    <!-- Products will be loaded here -->
                </div>

                <div id="no-products" class="text-center py-12 hidden">
                    <i data-lucide="shopping-bag" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No featured products available</h3>
                    <p class="text-gray-600 mb-6">Connect your WooCommerce store to display real products.</p>
                    <a href="shop.html" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Browse All Products
                    </a>
                </div>

                <div class="text-center mt-12" id="view-all-container" style="display: none;">
                    <a href="shop.html" class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                        View All Products
                        <i data-lucide="arrow-right" class="ml-2 w-5 h-5"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Best Sellers Section -->
    <section class="py-16 bg-gray-50" id="best-sellers-section" style="display: none;">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Best Sellers</h2>
                <p class="text-lg text-gray-600">Our most popular products loved by customers</p>
            </div>

            <div id="best-sellers-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Best sellers will be loaded here with badges -->
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
                <p class="text-lg text-gray-600">Find exactly what you're looking for</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                <a href="shop.html?category=electronics" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">📱</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Electronics</h3>
                </a>

                <a href="shop.html?category=fashion" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">👕</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Fashion</h3>
                </a>

                <a href="shop.html?category=home" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">🏠</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Home & Living</h3>
                </a>

                <a href="shop.html?category=sports" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">⚽</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Sports</h3>
                </a>

                <a href="shop.html?category=beauty" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">💄</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Beauty</h3>
                </a>

                <a href="shop.html?category=books" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">📚</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Books</h3>
                </a>
            </div>
        </div>
    </section>

    <!-- Recent Reviews Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header -->
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">What Our Customers Say</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Don't just take our word for it. Here's what real customers have to say about their experience with Deal4u.
                </p>
            </div>

            <!-- Reviews Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                <!-- Review 1 -->
                <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between mb-4">
                        <i data-lucide="quote" class="w-8 h-8 text-blue-600 opacity-20"></i>
                        <span class="text-xs text-gray-500">Dec 15, 2024</span>
                    </div>
                    <div class="mb-4">
                        <h3 class="font-semibold text-gray-900 mb-2">Amazing quality and fast shipping!</h3>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            I ordered a smartphone and it arrived within 2 days. The quality is exactly as described and the price was unbeatable. Highly recommend Deal4u!
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex text-yellow-400">
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://ui-avatars.com/api/?name=Sarah+Johnson&background=3b82f6&color=fff" alt="Sarah Johnson" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-medium text-gray-900 text-sm">Sarah Johnson</p>
                                <p class="text-xs text-green-600">Verified Purchase</p>
                            </div>
                        </div>
                        <a href="shop.html" class="text-blue-600 hover:text-blue-700 text-xs font-medium">View Product</a>
                    </div>
                </div>

                <!-- Review 2 -->
                <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between mb-4">
                        <i data-lucide="quote" class="w-8 h-8 text-blue-600 opacity-20"></i>
                        <span class="text-xs text-gray-500">Dec 12, 2024</span>
                    </div>
                    <div class="mb-4">
                        <h3 class="font-semibold text-gray-900 mb-2">Excellent customer service</h3>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            Had an issue with my order and their support team resolved it immediately. Great communication and very professional. Will definitely shop here again.
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex text-yellow-400">
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://ui-avatars.com/api/?name=Mike+Chen&background=3b82f6&color=fff" alt="Mike Chen" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-medium text-gray-900 text-sm">Mike Chen</p>
                                <p class="text-xs text-green-600">Verified Purchase</p>
                            </div>
                        </div>
                        <a href="shop.html" class="text-blue-600 hover:text-blue-700 text-xs font-medium">View Product</a>
                    </div>
                </div>

                <!-- Review 3 -->
                <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between mb-4">
                        <i data-lucide="quote" class="w-8 h-8 text-blue-600 opacity-20"></i>
                        <span class="text-xs text-gray-500">Dec 10, 2024</span>
                    </div>
                    <div class="mb-4">
                        <h3 class="font-semibold text-gray-900 mb-2">Best prices online!</h3>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            I compared prices across multiple sites and Deal4u had the best deals. The product quality exceeded my expectations. Fantastic experience overall!
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex text-yellow-400">
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://ui-avatars.com/api/?name=Emma+Davis&background=3b82f6&color=fff" alt="Emma Davis" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-medium text-gray-900 text-sm">Emma Davis</p>
                                <p class="text-xs text-green-600">Verified Purchase</p>
                            </div>
                        </div>
                        <a href="shop.html" class="text-blue-600 hover:text-blue-700 text-xs font-medium">View Product</a>
                    </div>
                </div>

                <!-- Review 4 -->
                <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between mb-4">
                        <i data-lucide="quote" class="w-8 h-8 text-blue-600 opacity-20"></i>
                        <span class="text-xs text-gray-500">Dec 8, 2024</span>
                    </div>
                    <div class="mb-4">
                        <h3 class="font-semibold text-gray-900 mb-2">Perfect packaging and delivery</h3>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            My order arrived perfectly packaged with no damage. The delivery was on time and the tracking system kept me informed throughout. Very impressed!
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex text-yellow-400">
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://ui-avatars.com/api/?name=James+Wilson&background=3b82f6&color=fff" alt="James Wilson" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-medium text-gray-900 text-sm">James Wilson</p>
                                <p class="text-xs text-green-600">Verified Purchase</p>
                            </div>
                        </div>
                        <a href="shop.html" class="text-blue-600 hover:text-blue-700 text-xs font-medium">View Product</a>
                    </div>
                </div>

                <!-- Review 5 -->
                <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between mb-4">
                        <i data-lucide="quote" class="w-8 h-8 text-blue-600 opacity-20"></i>
                        <span class="text-xs text-gray-500">Dec 5, 2024</span>
                    </div>
                    <div class="mb-4">
                        <h3 class="font-semibold text-gray-900 mb-2">Great variety of products</h3>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            Love the wide selection of products available. Found exactly what I was looking for at a great price. The website is easy to navigate too.
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex text-yellow-400">
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://ui-avatars.com/api/?name=Lisa+Brown&background=3b82f6&color=fff" alt="Lisa Brown" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-medium text-gray-900 text-sm">Lisa Brown</p>
                                <p class="text-xs text-green-600">Verified Purchase</p>
                            </div>
                        </div>
                        <a href="shop.html" class="text-blue-600 hover:text-blue-700 text-xs font-medium">View Product</a>
                    </div>
                </div>

                <!-- Review 6 -->
                <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between mb-4">
                        <i data-lucide="quote" class="w-8 h-8 text-blue-600 opacity-20"></i>
                        <span class="text-xs text-gray-500">Dec 3, 2024</span>
                    </div>
                    <div class="mb-4">
                        <h3 class="font-semibold text-gray-900 mb-2">Highly recommend Deal4u</h3>
                        <p class="text-gray-700 text-sm leading-relaxed">
                            This is my third order from Deal4u and they never disappoint. Quality products, competitive prices, and reliable service. My go-to online store!
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex text-yellow-400">
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://ui-avatars.com/api/?name=David+Miller&background=3b82f6&color=fff" alt="David Miller" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-medium text-gray-900 text-sm">David Miller</p>
                                <p class="text-xs text-green-600">Verified Purchase</p>
                            </div>
                        </div>
                        <a href="shop.html" class="text-blue-600 hover:text-blue-700 text-xs font-medium">View Product</a>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center">
                <a href="reviews.html" class="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    View All Reviews
                    <i data-lucide="arrow-right" class="w-4 h-4"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Promotional Banner -->
    <section class="py-16 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 overflow-hidden relative">
        <div class="absolute inset-0 opacity-20" style="background-image: url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80'); background-size: cover; background-position: center;"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="md:flex items-center justify-between">
                <div class="md:w-1/2 text-center md:text-left mb-8 md:mb-0">
                    <span class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm text-white text-xs font-semibold rounded-full mb-3">LIMITED TIME OFFER</span>
                    <h2 class="text-4xl md:text-5xl font-extrabold text-white mb-4 leading-tight">
                        Summer Sale is<br><span class="text-yellow-300">Now Live!</span>
                    </h2>
                    <p class="text-xl text-white text-opacity-90 mb-8 max-w-md">
                        Enjoy up to 50% off on selected items across all categories. Don't miss out on these incredible deals!
                    </p>
                    <a href="shop.html?sale=true" class="inline-flex items-center px-8 py-4 rounded-lg bg-white text-purple-600 font-bold text-lg hover:bg-yellow-300 hover:text-purple-700 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        Shop Now
                        <i data-lucide="arrow-right" class="ml-2 h-5 w-5"></i>
                    </a>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="w-64 h-64 sm:w-80 sm:h-80 relative rounded-full bg-white bg-opacity-20 backdrop-blur-sm p-2 transform rotate-3 hover:rotate-6 transition-transform">
                        <div class="w-full h-full rounded-full overflow-hidden border-4 border-white border-opacity-40">
                            <div class="w-full h-full bg-gradient-to-br from-yellow-400 to-pink-500 flex items-center justify-center text-white text-opacity-90 text-center p-6">
                                <div>
                                    <p class="text-4xl font-extrabold">50%</p>
                                    <p class="text-xl font-bold">OFF</p>
                                    <p class="text-sm mt-2 font-medium">Selected Items</p>
                                    <p class="text-xs mt-3 font-bold">Use code: SUMMER50</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trust Indicators -->
    <section class="py-12 bg-gray-50 border-t border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                <div class="flex flex-col items-center">
                    <i data-lucide="truck" class="h-8 w-8 text-blue-600 mb-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Free Shipping</h3>
                    <p class="text-sm text-gray-600">On orders over $50</p>
                </div>
                <div class="flex flex-col items-center">
                    <i data-lucide="shield" class="h-8 w-8 text-green-600 mb-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Secure Payment</h3>
                    <p class="text-sm text-gray-600">SSL protected checkout</p>
                </div>
                <div class="flex flex-col items-center">
                    <i data-lucide="refresh-cw" class="h-8 w-8 text-purple-600 mb-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Easy Returns</h3>
                    <p class="text-sm text-gray-600">30-day return policy</p>
                </div>
                <div class="flex flex-col items-center">
                    <i data-lucide="award" class="h-8 w-8 text-orange-600 mb-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Quality Guarantee</h3>
                    <p class="text-sm text-gray-600">Authentic products only</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <!-- Newsletter Section -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-3xl font-bold text-white mb-4">Stay Updated with Deal4u</h2>
                <p class="text-blue-100 mb-8 max-w-2xl mx-auto">
                    Subscribe to our newsletter and be the first to know about new products, exclusive deals, and special offers.
                </p>
                <form onsubmit="handleNewsletterSubmit(event)" class="flex flex-col sm:flex-row max-w-md mx-auto gap-4">
                    <input
                        type="email"
                        placeholder="Enter your email address"
                        id="newsletter-email"
                        required
                        class="flex-1 px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none text-gray-900"
                    />
                    <button
                        type="submit"
                        class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center"
                    >
                        <i data-lucide="send" class="w-4 h-4 mr-2"></i>
                        Subscribe
                    </button>
                </form>
                <p class="text-blue-200 text-sm mt-4">
                    Join over 50,000+ subscribers. Unsubscribe at any time.
                </p>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">

                <!-- Company Info -->
                <div class="lg:col-span-1">
                    <a href="/" class="inline-block">
                        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-bold text-2xl mb-6">
                            Deal4u
                        </div>
                    </a>
                    <p class="text-gray-300 mb-6 text-sm leading-relaxed">
                        Your ultimate destination for premium products at unbeatable prices. We bring you the best deals from around the world with fast, reliable shipping and exceptional customer service.
                    </p>

                    <!-- Social Media -->
                    <div class="flex space-x-4">
                        <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" class="bg-gray-800 p-3 rounded-full hover:bg-blue-600 transition-colors group" aria-label="Facebook">
                            <i data-lucide="facebook" class="w-5 h-5 text-gray-400 group-hover:text-white"></i>
                        </a>
                        <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" class="bg-gray-800 p-3 rounded-full hover:bg-blue-400 transition-colors group" aria-label="Twitter">
                            <i data-lucide="twitter" class="w-5 h-5 text-gray-400 group-hover:text-white"></i>
                        </a>
                        <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" class="bg-gray-800 p-3 rounded-full hover:bg-pink-600 transition-colors group" aria-label="Instagram">
                            <i data-lucide="instagram" class="w-5 h-5 text-gray-400 group-hover:text-white"></i>
                        </a>
                        <a href="https://youtube.com" target="_blank" rel="noopener noreferrer" class="bg-gray-800 p-3 rounded-full hover:bg-red-600 transition-colors group" aria-label="YouTube">
                            <i data-lucide="youtube" class="w-5 h-5 text-gray-400 group-hover:text-white"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-6 text-white">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="about.html" class="text-gray-300 hover:text-white transition-colors text-sm">About Us</a></li>
                        <li><a href="contact.html" class="text-gray-300 hover:text-white transition-colors text-sm">Contact Us</a></li>
                        <li><a href="faq.html" class="text-gray-300 hover:text-white transition-colors text-sm">FAQ</a></li>
                        <li><a href="shipping.html" class="text-gray-300 hover:text-white transition-colors text-sm">Shipping Info</a></li>
                        <li><a href="returns.html" class="text-gray-300 hover:text-white transition-colors text-sm">Return Policy</a></li>
                        <li><a href="size-guide.html" class="text-gray-300 hover:text-white transition-colors text-sm">Size Guide</a></li>
                        <li><a href="track-order.html" class="text-gray-300 hover:text-white transition-colors text-sm">Track Order</a></li>
                        <li><a href="gift-cards.html" class="text-gray-300 hover:text-white transition-colors text-sm">Gift Cards</a></li>
                    </ul>
                </div>

                <!-- Shop Categories -->
                <div>
                    <h3 class="text-lg font-semibold mb-6 text-white">Shop Categories</h3>
                    <ul class="space-y-3">
                        <li><a href="shop.html?category=electronics" class="text-gray-300 hover:text-white transition-colors text-sm">Electronics & Gadgets</a></li>
                        <li><a href="shop.html?category=home" class="text-gray-300 hover:text-white transition-colors text-sm">Home & Living</a></li>
                        <li><a href="shop.html?category=fashion" class="text-gray-300 hover:text-white transition-colors text-sm">Fashion & Accessories</a></li>
                        <li><a href="shop.html?category=beauty" class="text-gray-300 hover:text-white transition-colors text-sm">Health & Beauty</a></li>
                        <li><a href="shop.html?category=sports" class="text-gray-300 hover:text-white transition-colors text-sm">Sports & Outdoors</a></li>
                        <li><a href="shop.html?category=automotive" class="text-gray-300 hover:text-white transition-colors text-sm">Automotive</a></li>
                        <li><a href="shop.html?category=kids" class="text-gray-300 hover:text-white transition-colors text-sm">Kids & Baby</a></li>
                        <li><a href="shop.html?category=books" class="text-gray-300 hover:text-white transition-colors text-sm">Books & Media</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-6 text-white">Get in Touch</h3>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <i data-lucide="mail" class="w-5 h-5 text-blue-400 mt-1 flex-shrink-0"></i>
                            <div>
                                <p class="text-white font-medium">Email Us</p>
                                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition-colors text-sm">
                                    <EMAIL>
                                </a>
                                <p class="text-gray-400 text-xs mt-1">24/7 Support</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <i data-lucide="phone" class="w-5 h-5 text-green-400 mt-1 flex-shrink-0"></i>
                            <div>
                                <p class="text-white font-medium">Call Us</p>
                                <a href="tel:+447447186806" class="text-green-400 hover:text-green-300 transition-colors text-sm">
                                    +44 7447 186806
                                </a>
                                <p class="text-gray-400 text-xs mt-1">Mon-Fri, 9AM-6PM CET</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <i data-lucide="message-circle" class="w-5 h-5 text-green-500 mt-1 flex-shrink-0"></i>
                            <div>
                                <p class="text-white font-medium">WhatsApp Support</p>
                                <a href="https://wa.me/447447186806" target="_blank" rel="noopener noreferrer" class="text-green-400 hover:text-green-300 transition-colors text-sm">
                                    +44 7447 186806
                                </a>
                                <p class="text-gray-400 text-xs mt-1">Available 24/7</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <i data-lucide="map-pin" class="w-5 h-5 text-purple-400 mt-1 flex-shrink-0"></i>
                            <div>
                                <p class="text-white font-medium">Visit Us</p>
                                <p class="text-gray-400 text-sm">
                                    Fröbelstraße 12<br>
                                    41515 Grevenbroich<br>
                                    Germany
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Trust Badges -->
                    <div class="mt-8 space-y-3">
                        <h4 class="text-white font-semibold text-sm">Why Choose Deal4u?</h4>
                        <div class="space-y-4 text-sm">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="award" class="w-4 h-4 text-orange-400"></i>
                                <span class="text-gray-300 text-xs">Quality Guaranteed</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i data-lucide="shield" class="w-4 h-4 text-green-400"></i>
                                <span class="text-gray-300 text-xs">SSL Secured</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i data-lucide="truck" class="w-4 h-4 text-blue-400"></i>
                                <span class="text-gray-300 text-xs">Fast Delivery</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i data-lucide="refresh-cw" class="w-4 h-4 text-purple-400"></i>
                                <span class="text-gray-300 text-xs">Easy Returns</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods & Bottom -->
        <div class="border-t border-gray-800 bg-gray-950">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">

                    <!-- Payment Methods -->
                    <div>
                        <h4 class="text-white font-semibold mb-3 text-sm">We Accept</h4>
                        <div class="flex gap-3">
                            <div class="bg-white rounded-md p-2 flex items-center justify-center w-12 h-8">
                                <span class="text-blue-600 font-bold text-xs">VISA</span>
                            </div>
                            <div class="bg-white rounded-md p-2 flex items-center justify-center w-12 h-8">
                                <span class="text-red-600 font-bold text-xs">MC</span>
                            </div>
                            <div class="bg-white rounded-md p-2 flex items-center justify-center w-12 h-8">
                                <span class="text-blue-500 font-bold text-xs">AMEX</span>
                            </div>
                            <div class="bg-blue-600 rounded-md p-2 flex items-center justify-center w-12 h-8">
                                <span class="text-white font-bold text-xs">PP</span>
                            </div>
                            <div class="bg-black rounded-md p-2 flex items-center justify-center w-12 h-8">
                                <span class="text-white font-bold text-xs">A</span>
                            </div>
                        </div>
                    </div>

                    <!-- Copyright & Legal -->
                    <div class="text-center md:text-right">
                        <p class="text-gray-400 text-sm mb-2">
                            © 2024 Deal4u. All rights reserved.
                        </p>
                        <div class="flex flex-wrap justify-center md:justify-end gap-4 text-sm">
                            <a href="privacy.html" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                            <a href="terms.html" class="text-gray-400 hover:text-white transition-colors">Terms of Service</a>
                            <a href="cookies.html" class="text-gray-400 hover:text-white transition-colors">Cookie Policy</a>
                            <a href="accessibility.html" class="text-gray-400 hover:text-white transition-colors">Accessibility</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security & Certifications -->
        <div class="bg-gray-900 border-t border-gray-800 py-4">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col md:flex-row justify-center items-center space-y-2 md:space-y-0 md:space-x-8 text-center">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="shield" class="w-4 h-4 text-green-400"></i>
                        <span class="text-gray-400 text-xs">SSL Secured</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="award" class="w-4 h-4 text-blue-400"></i>
                        <span class="text-gray-400 text-xs">Verified Business</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4 text-purple-400"></i>
                        <span class="text-gray-400 text-xs">30-Day Returns</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="truck" class="w-4 h-4 text-orange-400"></i>
                        <span class="text-gray-400 text-xs">Free Shipping Over $50</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- PWA Install Prompt -->
    <div id="pwa-install-prompt" class="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4">
            <!-- Header -->
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="smartphone" class="w-6 h-6 text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 text-sm">Install Deal4u App</h3>
                        <p class="text-xs text-gray-500">Get the full app experience</p>
                    </div>
                </div>
                <button onclick="dismissPWAPrompt()" class="text-gray-400 hover:text-gray-600 p-1">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>

            <!-- Benefits -->
            <div class="mb-4">
                <div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        Offline browsing
                    </div>
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                        Faster loading
                    </div>
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                        Push notifications
                    </div>
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
                        Home screen access
                    </div>
                </div>
            </div>

            <!-- Install Instructions -->
            <div id="pwa-instructions" class="mb-4 p-3 bg-green-50 rounded-lg">
                <p class="text-xs text-green-800">
                    Install our app for the best shopping experience with offline access and notifications.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-2">
                <button onclick="installPWA()" id="pwa-install-btn" class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors flex items-center justify-center">
                    <i data-lucide="download" class="w-4 h-4 mr-1"></i>
                    Install
                </button>
                <button onclick="remindLaterPWA()" class="flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">
                    Later
                </button>
                <button onclick="dismissPWAPrompt()" class="px-3 py-2 text-gray-500 hover:text-gray-700 text-sm">
                    Don't show
                </button>
            </div>
        </div>
    </div>

    <!-- PWA Status Indicator -->
    <div id="pwa-status" class="fixed top-4 right-4 z-40 hidden">
        <div class="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <div class="flex items-center">
                <div class="w-2 h-2 rounded-full mr-2 bg-green-500"></div>
                <span id="pwa-status-text">Online</span>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button
        id="back-to-top"
        class="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40 hidden"
        onclick="scrollToTop()"
        aria-label="Back to top"
    >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- WhatsApp Button -->
    <div id="whatsapp-container" class="fixed bottom-6 right-6 z-50 transition-all duration-300 scale-75 opacity-0">
        <a
            href="https://wa.me/447447186806"
            target="_blank"
            rel="noopener noreferrer"
            class="flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 rounded-full shadow-lg transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1 hover:scale-110 group"
            aria-label="Contact us on WhatsApp"
        >
            <div class="absolute animate-ping w-14 h-14 bg-green-400 rounded-full opacity-60"></div>
            <i data-lucide="message-circle" class="w-7 h-7 text-white"></i>
        </a>
        <div class="bg-black text-white text-xs font-medium py-1 px-2 rounded-md absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Chat with us
        </div>
    </div>

    <!-- Cart Sidebar -->
    <div id="cart-sidebar" class="cart-sidebar">
        <div class="cart-header">
            <h3>Shopping Cart</h3>
            <button onclick="toggleCart()" class="close-cart">&times;</button>
        </div>
        <div id="cart-items" class="cart-items">
            <p class="empty-cart">Your cart is empty</p>
        </div>
        <div class="cart-footer">
            <div class="cart-total">
                <strong>Total: £<span id="cart-total">0.00</span></strong>
            </div>
            <button class="checkout-btn" onclick="proceedToCheckout()">Proceed to Checkout</button>
        </div>
    </div>

    <!-- Auth Modal -->
    <div id="auth-modal" class="auth-modal">
        <div class="auth-content">
            <button class="auth-close" onclick="toggleAuth()">&times;</button>
            <div id="login-form" class="auth-form">
                <h2>Login to Deal4u</h2>
                <form onsubmit="handleLogin(event)">
                    <input type="email" placeholder="Email" required>
                    <input type="password" placeholder="Password" required>
                    <button type="submit">Login</button>
                </form>
                <p>Don't have an account? <a href="#" onclick="showRegister()">Register here</a></p>
            </div>
            <div id="register-form" class="auth-form" style="display: none;">
                <h2>Register for Deal4u</h2>
                <form onsubmit="handleRegister(event)">
                    <input type="text" placeholder="Full Name" required>
                    <input type="email" placeholder="Email" required>
                    <input type="password" placeholder="Password" required>
                    <input type="password" placeholder="Confirm Password" required>
                    <button type="submit">Register</button>
                </form>
                <p>Already have an account? <a href="#" onclick="showLogin()">Login here</a></p>
            </div>
        </div>
    </div>

    <!-- Product Modal -->
    <div id="product-modal" class="product-modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeProductModal()">&times;</button>
            <div class="modal-product-grid">
                <div class="modal-image-gallery">
                    <div class="image-slider-container">
                        <img id="modal-main-image" class="modal-main-image" src="" alt="">
                        <button class="slider-nav slider-prev" onclick="prevImage()">‹</button>
                        <button class="slider-nav slider-next" onclick="nextImage()">›</button>
                        <div class="image-counter">
                            <span id="current-image">1</span> / <span id="total-images">1</span>
                        </div>
                    </div>
                    <div id="image-dots" class="image-dots"></div>
                </div>
                <div class="modal-product-info">
                    <h2 id="modal-product-title" class="modal-product-title"></h2>
                    <div id="modal-product-price" class="modal-product-price"></div>
                    <div id="modal-product-rating" class="product-rating"></div>
                    <div id="modal-product-description" class="modal-product-description"></div>
                    <div class="modal-actions">
                        <button class="modal-btn primary" onclick="addToCartFromModal()">Add to Cart</button>
                        <button class="modal-btn secondary" onclick="addToWishlist()">Add to Wishlist</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Global functions for the new design
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('mobile-menu-icon');

            if (mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
                menuIcon.setAttribute('data-lucide', 'x');
            } else {
                mobileMenu.classList.add('hidden');
                menuIcon.setAttribute('data-lucide', 'menu');
            }
            lucide.createIcons();
        }

        function toggleUserMenu() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('hidden');
        }

        function handleSearchSubmit(event) {
            event.preventDefault();
            const searchInput = event.target.querySelector('input[type="text"]');
            const query = searchInput.value.trim();
            if (query) {
                window.location.href = `shop.html?search=${encodeURIComponent(query)}`;
            }
        }

        function handleSearchInput(input) {
            const submitBtn = document.getElementById('search-submit-btn');
            const mobileSubmitBtn = document.getElementById('mobile-search-submit-btn');

            if (input.value.trim()) {
                if (submitBtn) submitBtn.classList.remove('hidden');
                if (mobileSubmitBtn) mobileSubmitBtn.classList.remove('hidden');
            } else {
                if (submitBtn) submitBtn.classList.add('hidden');
                if (mobileSubmitBtn) mobileSubmitBtn.classList.add('hidden');
            }
        }

        function openImageSearch() {
            document.getElementById('image-search-input').click();
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                // Implement image search functionality
                alert('Image search feature coming soon!');
            }
        }

        function handleNewsletterSubmit(event) {
            event.preventDefault();
            const email = document.getElementById('newsletter-email').value;
            if (email) {
                alert('Thank you for subscribing! You will receive our latest deals and updates.');
                document.getElementById('newsletter-email').value = '';
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('user-dropdown');
            const userMenuBtn = document.getElementById('user-menu-btn');

            if (!userMenuBtn.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // Animate stats on scroll
        function animateStats() {
            const stats = [
                { id: 'stat-customers', target: 50000, suffix: ',000+', display: '50,000+' },
                { id: 'stat-products', target: 1000000, suffix: 'M+', display: '1M+' },
                { id: 'stat-countries', target: 25, suffix: '+', display: '25+' },
                { id: 'stat-experience', target: 5, suffix: '+', display: '5+' }
            ];

            stats.forEach(stat => {
                const element = document.getElementById(stat.id);
                if (element) {
                    let current = 0;
                    const increment = stat.target / 100;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= stat.target) {
                            current = stat.target;
                            clearInterval(timer);
                            element.textContent = stat.display;
                        } else {
                            if (stat.id === 'stat-customers') {
                                element.textContent = Math.floor(current / 1000) + ',000+';
                            } else if (stat.id === 'stat-products') {
                                element.textContent = (current / 1000000).toFixed(1) + 'M+';
                            } else {
                                element.textContent = Math.floor(current) + '+';
                            }
                        }
                    }, 20);
                }
            });
        }

        // Scroll to top function
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Handle scroll events for buttons
        function handleScroll() {
            const scrollY = window.scrollY;
            const backToTopBtn = document.getElementById('back-to-top');
            const whatsappContainer = document.getElementById('whatsapp-container');

            // Show/hide back to top button
            if (scrollY > 300) {
                backToTopBtn.classList.remove('hidden');
            } else {
                backToTopBtn.classList.add('hidden');
            }

            // Show/hide WhatsApp button
            if (scrollY > 300) {
                whatsappContainer.classList.remove('scale-75', 'opacity-0');
                whatsappContainer.classList.add('scale-100', 'opacity-100');
            } else {
                whatsappContainer.classList.remove('scale-100', 'opacity-100');
                whatsappContainer.classList.add('scale-75', 'opacity-0');
            }

            // Update header background on scroll
            const header = document.getElementById('header');
            if (scrollY > 10) {
                header.classList.remove('bg-white', 'shadow-md');
                header.classList.add('bg-white/95', 'backdrop-blur-md', 'shadow-lg');
            } else {
                header.classList.remove('bg-white/95', 'backdrop-blur-md', 'shadow-lg');
                header.classList.add('bg-white', 'shadow-md');
            }
        }

        // PWA functionality
        let deferredPrompt = null;

        function initPWA() {
            // Check if running in standalone mode (already installed)
            const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                                window.navigator.standalone ||
                                document.referrer.includes('android-app://');

            if (isStandalone) {
                document.getElementById('pwa-status').classList.remove('hidden');
                updateOnlineStatus();
                return;
            }

            // Check if user has already dismissed the prompt
            const hasBeenDismissed = localStorage.getItem('pwa-install-dismissed');
            const lastDismissed = localStorage.getItem('pwa-install-dismissed-date');

            // Show prompt again after 7 days
            const shouldShowAgain = !lastDismissed ||
                                   (Date.now() - parseInt(lastDismissed)) > 7 * 24 * 60 * 60 * 1000;

            // Listen for the beforeinstallprompt event
            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                deferredPrompt = e;

                // Show prompt if not dismissed recently
                if (!hasBeenDismissed && shouldShowAgain) {
                    setTimeout(() => {
                        document.getElementById('pwa-install-prompt').classList.remove('hidden');
                    }, 3000);
                }
            });

            // For iOS, show manual install instructions
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            if (isIOS && !hasBeenDismissed && shouldShowAgain) {
                setTimeout(() => {
                    const instructions = document.getElementById('pwa-instructions');
                    instructions.className = 'mb-4 p-3 bg-blue-50 rounded-lg';
                    instructions.innerHTML = `
                        <p class="text-xs text-blue-800 mb-2 font-medium">To install on iOS:</p>
                        <ol class="text-xs text-blue-700 space-y-1">
                            <li>1. Tap the share button in Safari</li>
                            <li>2. Scroll down and tap "Add to Home Screen"</li>
                            <li>3. Tap "Add" to install</li>
                        </ol>
                    `;
                    document.getElementById('pwa-install-btn').style.display = 'none';
                    document.getElementById('pwa-install-prompt').classList.remove('hidden');
                }, 5000);
            }
        }

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((result) => {
                    if (result.outcome === 'accepted') {
                        console.log('PWA installation accepted');
                        document.getElementById('pwa-install-prompt').classList.add('hidden');
                    }
                    deferredPrompt = null;
                });
            }
        }

        function dismissPWAPrompt() {
            document.getElementById('pwa-install-prompt').classList.add('hidden');
            localStorage.setItem('pwa-install-dismissed', 'true');
            localStorage.setItem('pwa-install-dismissed-date', Date.now().toString());
        }

        function remindLaterPWA() {
            document.getElementById('pwa-install-prompt').classList.add('hidden');
        }

        function updateOnlineStatus() {
            const statusElement = document.getElementById('pwa-status-text');
            const statusIndicator = document.querySelector('#pwa-status .w-2');

            if (navigator.onLine) {
                statusElement.textContent = 'Online';
                statusIndicator.className = 'w-2 h-2 rounded-full mr-2 bg-green-500';
                document.getElementById('pwa-status').className = 'fixed top-4 right-4 z-40 px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
            } else {
                statusElement.textContent = 'Offline';
                statusIndicator.className = 'w-2 h-2 rounded-full mr-2 bg-red-500';
                document.getElementById('pwa-status').className = 'fixed top-4 right-4 z-40 px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800';
            }
        }

        // Service Worker Registration
        function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', async () => {
                    try {
                        const registration = await navigator.serviceWorker.register('/sw.js', {
                            scope: '/'
                        });

                        console.log('Service Worker registered successfully:', registration);

                        // Handle updates
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;

                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    showUpdateNotification();
                                }
                            });
                        });

                    } catch (error) {
                        console.error('Service Worker registration failed:', error);
                    }
                });
            }
        }

        function showUpdateNotification() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>New version available!</span>
                    <button onclick="window.location.reload()" class="ml-4 bg-white text-blue-600 px-3 py-1 rounded text-sm">
                        Update
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 10000);
        }

        // Smooth scroll for anchor links
        function handleAnchorClick(e) {
            if (e.target.tagName === 'A' && e.target.getAttribute('href') && e.target.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(e.target.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            }
        }

        // Performance monitoring
        function monitorPerformance() {
            if ('performance' in window) {
                window.addEventListener('load', function() {
                    setTimeout(function() {
                        if (window.performance.timing) {
                            const perfData = window.performance.timing;
                            const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
                            console.log('Page load time:', pageLoadTime + 'ms');
                        }
                    }, 0);
                });
            }
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Add scroll event listener
            window.addEventListener('scroll', handleScroll);

            // Initialize PWA
            initPWA();
            registerServiceWorker();

            // Listen for online/offline events
            window.addEventListener('online', updateOnlineStatus);
            window.addEventListener('offline', updateOnlineStatus);

            // Add smooth scroll for anchor links
            document.addEventListener('click', handleAnchorClick);

            // Monitor performance
            monitorPerformance();

            // Animate stats when they come into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateStats();
                        observer.unobserve(entry.target);
                    }
                });
            });

            const statsSection = document.querySelector('#stat-products')?.closest('section');
            if (statsSection) {
                observer.observe(statsSection);
            }
        });
    </script>

    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/woocommerce-api.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/products.js"></script>
    <script src="js/main.js"></script>
    <script src="sw-register.js"></script>
</body>
</html>
