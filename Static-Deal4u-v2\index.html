<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deal4u - Amazing Deals on Premium Products</title>

    <!-- Preconnect to external domains for better performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://images.unsplash.com">

    <!-- Favicon and app icons -->
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="manifest" href="manifest.json">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Deal4u">
    <meta name="application-name" content="Deal4u">
    <meta name="msapplication-TileColor" content="#3b82f6">
    <meta name="msapplication-tooltip" content="Deal4u - Amazing Deals">
    <meta name="msapplication-starturl" content="/">
    <meta name="msapplication-navbutton-color" content="#3b82f6">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.">
    <meta name="keywords" content="ecommerce, deals, shopping, products, online store">
    <meta name="author" content="Deal4u Team">

    <!-- Open Graph -->
    <meta property="og:type" content="website">
    <meta property="og:locale" content="en_US">
    <meta property="og:site_name" content="Deal4u">
    <meta property="og:title" content="Deal4u - Amazing Deals on Premium Products">
    <meta property="og:description" content="Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.">
    <meta property="og:image" content="/og-image.jpg">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@deal4u">
    <meta name="twitter:creator" content="@deal4u">
    <meta name="twitter:title" content="Deal4u - Amazing Deals on Premium Products">
    <meta name="twitter:description" content="Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.">
    <meta name="twitter:image" content="/og-image.jpg">

    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'spin-slow': 'spin 3s linear infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'pulse-slow': 'pulse 3s infinite',
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">

    <!-- Prevent FOUC -->
    <script>
        try {
            const mode = localStorage.getItem('theme');
            if (mode && mode === 'dark') {
                document.documentElement.classList.add('dark');
            }
        } catch (e) {
            console.error('Error accessing localStorage:', e);
        }
    </script>
</head>
<body class="antialiased min-h-screen flex flex-col bg-gray-50">
    <!-- Header -->
    <header id="header" class="sticky top-0 z-50 transition-all duration-300 bg-white shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <a href="/" class="flex items-center space-x-2 flex-shrink-0">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-lg font-bold text-xl hover:from-blue-700 hover:to-purple-700 transition-colors">
                        Deal4u
                    </div>
                </a>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-1">
                    <a href="/" class="px-3 py-2 rounded-md text-sm font-medium text-blue-600 bg-blue-50">Home</a>
                    <a href="shop.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Shop</a>
                    <a href="categories.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Categories</a>
                    <a href="track-order.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Track Order</a>
                    <a href="about.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">About</a>
                    <a href="contact.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Contact</a>
                    <a href="faq.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">FAQ</a>
                </nav>

                <!-- Search Bar - Desktop -->
                <div class="hidden md:flex flex-1 max-w-lg mx-8">
                    <form onsubmit="handleSearchSubmit(event)" class="relative w-full">
                        <div class="relative">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                            <input
                                type="text"
                                placeholder="Search products..."
                                id="search-input"
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                            />
                            <button
                                type="button"
                                onclick="openImageSearch()"
                                class="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600 transition-colors"
                                title="Search by image"
                            >
                                <i data-lucide="camera" class="w-5 h-5"></i>
                            </button>
                            <input
                                type="file"
                                accept="image/*"
                                class="hidden"
                                id="image-search-input"
                                onchange="handleImageUpload(event)"
                            />
                            <button
                                type="submit"
                                id="search-submit-btn"
                                class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 hidden"
                            >
                                Search
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Right Side Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Wishlist - Desktop -->
                    <a href="wishlist.html" class="hidden md:flex items-center space-x-1 text-gray-700 hover:text-red-500 transition-colors">
                        <i data-lucide="heart" class="w-5 h-5"></i>
                        <span class="text-sm">Wishlist</span>
                    </a>

                    <!-- Shopping Cart -->
                    <a href="cart.html" class="relative flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors">
                        <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                        <span class="hidden md:block text-sm">Cart</span>
                        <span id="cart-count" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                    </a>

                    <!-- User Menu -->
                    <div class="relative">
                        <button
                            id="user-menu-btn"
                            onclick="toggleUserMenu()"
                            class="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                            <i data-lucide="user" class="w-5 h-5"></i>
                            <span class="hidden md:block text-sm" id="user-menu-text">Login</span>
                        </button>

                        <!-- User Dropdown -->
                        <div id="user-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50 hidden">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <button
                        class="md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors"
                        onclick="toggleMobileMenu()"
                        id="mobile-menu-btn"
                    >
                        <i data-lucide="menu" class="w-6 h-6" id="mobile-menu-icon"></i>
                    </button>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="md:hidden bg-white border-t border-gray-200 py-4 hidden">
                <!-- Mobile Search -->
                <div class="px-2 pb-4">
                    <form onsubmit="handleSearchSubmit(event)" class="relative">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                        <input
                            type="text"
                            placeholder="Search products..."
                            id="mobile-search-input"
                            class="w-full pl-10 pr-16 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <button
                            type="button"
                            onclick="openImageSearch()"
                            class="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600 transition-colors"
                            title="Search by image"
                        >
                            <i data-lucide="camera" class="w-5 h-5"></i>
                        </button>
                        <button
                            type="submit"
                            class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                        >
                            Search
                        </button>
                    </form>
                </div>

                <!-- Mobile Navigation -->
                <div class="space-y-1">
                    <a href="/" class="block px-3 py-2 rounded-md text-base font-medium text-blue-600 bg-blue-50">Home</a>
                    <a href="shop.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Shop</a>
                    <a href="categories.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Categories</a>
                    <a href="track-order.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Track Order</a>
                    <a href="about.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">About</a>
                    <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Contact</a>
                    <a href="faq.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">FAQ</a>
                    <a href="wishlist.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-red-500 hover:bg-gray-50 transition-colors">Wishlist</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">
        <!-- SALE RIBBON - Prominent at the top -->
        <div class="absolute top-0 left-0 right-0 bg-yellow-400 text-purple-900 py-2 px-4 text-center font-bold transform -skew-y-2 shadow-lg z-30">
            <div class="container mx-auto flex items-center justify-center gap-4">
                <span class="animate-pulse">🔥</span>
                <span class="text-lg uppercase tracking-wider">SUMMER SALE: UP TO 50% OFF EVERYTHING</span>
                <span class="animate-pulse">🔥</span>
            </div>
        </div>

        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E'); background-size: 30px 30px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24 relative z-10">
            <!-- Promotional Badge - Floating -->
            <div class="absolute top-4 right-4 md:top-8 md:right-8 animate-bounce-slow">
                <div class="relative w-24 h-24 md:w-32 md:h-32 bg-yellow-400 rounded-full flex items-center justify-center transform rotate-12 shadow-xl">
                    <div class="text-center px-2">
                        <p class="text-xl md:text-2xl font-black text-purple-900 leading-none">50%</p>
                        <p class="text-sm md:text-base font-bold text-purple-900 leading-none">OFF</p>
                        <p class="text-[10px] md:text-xs mt-1 font-semibold text-purple-800">SUMMER50</p>
                    </div>
                </div>
            </div>

            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-2/3 text-center md:text-left">
                    <div class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full mb-6 animate-pulse">
                        <span class="text-xs font-semibold tracking-wider">🔥 SUMMER SALE NOW LIVE 🔥</span>
                    </div>

                    <h1 class="text-4xl md:text-6xl font-bold mb-6">
                        Premium Products,
                        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
                            Amazing Deals
                        </span>
                    </h1>
                    <p class="text-xl mb-8 max-w-2xl">
                        Discover thousands of high-quality products at unbeatable prices. Fast shipping, competitive deals, and exceptional customer service - that's the Deal4u promise.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 mb-8 md:mb-0">
                        <a
                            href="shop.html?sale=true"
                            class="bg-yellow-400 text-purple-900 px-8 py-3 rounded-lg font-bold hover:bg-yellow-300 transition-colors flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                        >
                            <span>Shop Summer Sale</span>
                            <i data-lucide="arrow-right" class="w-5 h-5"></i>
                        </a>
                        <a
                            href="shop.html"
                            class="bg-white bg-opacity-20 backdrop-blur-sm text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-700 transition-colors border border-white border-opacity-40"
                        >
                            Browse All
                        </a>
                    </div>
                </div>

                <!-- Image/Visual Element -->
                <div class="md:w-1/3 hidden md:block relative">
                    <div class="relative z-0 mt-8">
                        <div class="absolute -inset-4 bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500 rounded-full opacity-50 blur-xl animate-pulse-slow"></div>
                        <div class="relative bg-white bg-opacity-20 backdrop-blur-md rounded-2xl p-4 shadow-2xl border border-white border-opacity-20">
                            <!-- Dashed border on the card -->
                            <div class="absolute -inset-3 rounded-full border-2 border-dashed border-yellow-600/90 animate-spin-slow" style="z-index: 30;"></div>
                            <div class="aspect-square w-full max-w-[300px] flex flex-col items-center justify-between text-center p-6">
                                <!-- Top section -->
                                <div class="text-5xl font-black mt-2">SALE</div>

                                <!-- Middle section - main offer -->
                                <div class="flex flex-col items-center -mt-4">
                                    <div class="text-xl font-bold mb-1">Up to</div>
                                    <div class="text-7xl font-extrabold text-yellow-400 leading-none drop-shadow-lg">50%</div>
                                    <div class="text-2xl font-bold tracking-wider mt-1">OFF</div>
                                    <div class="h-px w-3/4 bg-white opacity-30 my-2"></div>
                                </div>

                                <!-- Bottom section -->
                                <div class="text-sm font-medium py-1 px-4 bg-white bg-opacity-20 rounded-full shadow-inner">Limited Time Only</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trust Indicators -->
            <div class="mt-12 flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
                <div class="flex items-center space-x-2">
                    <div class="flex text-yellow-400">
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                        <i data-lucide="star" class="w-5 h-5 fill-current"></i>
                    </div>
                    <span class="text-blue-100">4.9/5 from 10,000+ reviews</span>
                </div>
                <div class="text-blue-100">
                    📦 Free shipping on orders £50+
                </div>
                <div class="text-blue-100">
                    🔒 Secure checkout guaranteed
                </div>
            </div>
        </div>

        <!-- Wave Decoration -->
        <div class="absolute bottom-0 left-0 right-0">
            <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full h-auto">
                <path d="M0 120L48 105C96 90 192 60 288 55C384 50 480 70 576 75C672 80 768 70 864 65C960 60 1056 60 1152 65C1248 70 1344 80 1392 85L1440 90V120H1392C1344 120 1248 120 1152 120C1056 120 960 120 864 120C768 120 672 120 576 120C480 120 384 120 288 120C192 120 96 120 48 120H0Z" fill="white" fill-opacity="0.05"/>
                <path d="M0 120L48 110C96 100 192 80 288 75C384 70 480 80 576 85C672 90 768 90 864 85C960 80 1056 70 1152 75C1248 80 1344 100 1392 110L1440 120V120H1392C1344 120 1248 120 1152 120C1056 120 960 120 864 120C768 120 672 120 576 120C480 120 384 120 288 120C192 120 96 120 48 120H0Z" fill="white" fill-opacity="0.1"/>
            </svg>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose Deal4u?</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    We provide the best shopping experience with premium products and exceptional service
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                        <i data-lucide="truck" class="w-8 h-8 text-blue-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Free Shipping</h3>
                    <p class="text-gray-600">Free shipping on orders over £50</p>
                </div>

                <div class="text-center group">
                    <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                        <i data-lucide="shield" class="w-8 h-8 text-green-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Secure Payment</h3>
                    <p class="text-gray-600">SSL protected checkout</p>
                </div>

                <div class="text-center group">
                    <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                        <i data-lucide="refresh-cw" class="w-8 h-8 text-purple-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Easy Returns</h3>
                    <p class="text-gray-600">30-day return policy</p>
                </div>

                <div class="text-center group">
                    <div class="bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors">
                        <i data-lucide="award" class="w-8 h-8 text-orange-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Quality Guarantee</h3>
                    <p class="text-gray-600">Authentic products only</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div>
                    <div class="text-3xl font-bold text-blue-600 mb-2" id="stat-products">1000+</div>
                    <div class="text-gray-600">Products</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-green-600 mb-2" id="stat-customers">50K+</div>
                    <div class="text-gray-600">Happy Customers</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-purple-600 mb-2" id="stat-orders">100K+</div>
                    <div class="text-gray-600">Orders Delivered</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-orange-600 mb-2" id="stat-rating">4.9★</div>
                    <div class="text-gray-600">Average Rating</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Products</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Discover our hand-picked selection of premium products
                </p>
            </div>

            <div id="featured-products-container">
                <div class="text-center py-12" id="products-loading">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-4 text-gray-600">Loading amazing deals from your WooCommerce store...</p>
                </div>

                <div id="featured-products-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 hidden">
                    <!-- Products will be loaded here -->
                </div>

                <div id="no-products" class="text-center py-12 hidden">
                    <i data-lucide="shopping-bag" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No featured products available</h3>
                    <p class="text-gray-600 mb-6">Connect your WooCommerce store to display real products.</p>
                    <a href="shop.html" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Browse All Products
                    </a>
                </div>

                <div class="text-center mt-12" id="view-all-container" style="display: none;">
                    <a href="shop.html" class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                        View All Products
                        <i data-lucide="arrow-right" class="ml-2 w-5 h-5"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Best Sellers Section -->
    <section class="py-16 bg-gray-50" id="best-sellers-section" style="display: none;">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Best Sellers</h2>
                <p class="text-lg text-gray-600">Our most popular products loved by customers</p>
            </div>

            <div id="best-sellers-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Best sellers will be loaded here -->
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
                <p class="text-lg text-gray-600">Find exactly what you're looking for</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                <a href="shop.html?category=electronics" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">📱</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Electronics</h3>
                </a>

                <a href="shop.html?category=fashion" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">👕</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Fashion</h3>
                </a>

                <a href="shop.html?category=home" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">🏠</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Home & Living</h3>
                </a>

                <a href="shop.html?category=sports" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">⚽</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Sports</h3>
                </a>

                <a href="shop.html?category=beauty" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">💄</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Beauty</h3>
                </a>

                <a href="shop.html?category=books" class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300">
                    <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">📚</div>
                    <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">Books</h3>
                </a>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold mb-4">Stay Updated with Latest Deals</h2>
            <p class="text-xl mb-8 text-blue-100">Subscribe to our newsletter and never miss amazing deals and new arrivals!</p>
            <form onsubmit="handleNewsletterSubmit(event)" class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                    type="email"
                    placeholder="Enter your email address"
                    id="newsletter-email"
                    required
                    class="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                />
                <button
                    type="submit"
                    class="px-8 py-3 bg-yellow-400 text-purple-900 font-semibold rounded-lg hover:bg-yellow-300 transition-colors"
                >
                    Subscribe
                </button>
            </form>
            <p class="text-sm text-blue-200 mt-4">
                Join 50,000+ subscribers and get exclusive deals delivered to your inbox!
            </p>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-lg font-bold text-xl">
                            Deal4u
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-sm">
                        Your trusted partner for premium products at amazing deals. Quality guaranteed, fast shipping, and exceptional customer service.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="facebook" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="twitter" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="instagram" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="youtube" class="w-5 h-5"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="shop.html" class="text-gray-300 hover:text-white transition-colors">Shop</a></li>
                        <li><a href="categories.html" class="text-gray-300 hover:text-white transition-colors">Categories</a></li>
                        <li><a href="track-order.html" class="text-gray-300 hover:text-white transition-colors">Track Order</a></li>
                        <li><a href="wishlist.html" class="text-gray-300 hover:text-white transition-colors">Wishlist</a></li>
                        <li><a href="about.html" class="text-gray-300 hover:text-white transition-colors">About Us</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Customer Service</h3>
                    <ul class="space-y-2">
                        <li><a href="contact.html" class="text-gray-300 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="faq.html" class="text-gray-300 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="returns.html" class="text-gray-300 hover:text-white transition-colors">Returns</a></li>
                        <li><a href="shipping.html" class="text-gray-300 hover:text-white transition-colors">Shipping Info</a></li>
                        <li><a href="support.html" class="text-gray-300 hover:text-white transition-colors">Support</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="mail" class="w-5 h-5 text-blue-400"></i>
                            <span class="text-gray-300"><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i data-lucide="phone" class="w-5 h-5 text-green-400"></i>
                            <span class="text-gray-300">+447447186806</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i data-lucide="map-pin" class="w-5 h-5 text-red-400"></i>
                            <span class="text-gray-300">London, UK</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i data-lucide="clock" class="w-5 h-5 text-yellow-400"></i>
                            <span class="text-gray-300">Mon-Fri: 9AM-6PM</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    &copy; 2024 Deal4u. All rights reserved.
                </p>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="privacy.html" class="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
                    <a href="terms.html" class="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</a>
                    <a href="cookies.html" class="text-gray-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Button -->
    <a
        href="https://wa.me/447447186806?text=Hello! I'm interested in your products on Deal4u."
        class="fixed bottom-6 left-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 group"
        id="whatsapp-btn"
        title="Chat with us on WhatsApp"
    >
        <svg class="w-6 h-6 group-hover:scale-110 transition-transform" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.515"/>
        </svg>
        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
            !
        </span>
    </a>

    <!-- Cart Sidebar -->
    <div id="cart-sidebar" class="cart-sidebar">
        <div class="cart-header">
            <h3>Shopping Cart</h3>
            <button onclick="toggleCart()" class="close-cart">&times;</button>
        </div>
        <div id="cart-items" class="cart-items">
            <p class="empty-cart">Your cart is empty</p>
        </div>
        <div class="cart-footer">
            <div class="cart-total">
                <strong>Total: £<span id="cart-total">0.00</span></strong>
            </div>
            <button class="checkout-btn" onclick="proceedToCheckout()">Proceed to Checkout</button>
        </div>
    </div>

    <!-- Auth Modal -->
    <div id="auth-modal" class="auth-modal">
        <div class="auth-content">
            <button class="auth-close" onclick="toggleAuth()">&times;</button>
            <div id="login-form" class="auth-form">
                <h2>Login to Deal4u</h2>
                <form onsubmit="handleLogin(event)">
                    <input type="email" placeholder="Email" required>
                    <input type="password" placeholder="Password" required>
                    <button type="submit">Login</button>
                </form>
                <p>Don't have an account? <a href="#" onclick="showRegister()">Register here</a></p>
            </div>
            <div id="register-form" class="auth-form" style="display: none;">
                <h2>Register for Deal4u</h2>
                <form onsubmit="handleRegister(event)">
                    <input type="text" placeholder="Full Name" required>
                    <input type="email" placeholder="Email" required>
                    <input type="password" placeholder="Password" required>
                    <input type="password" placeholder="Confirm Password" required>
                    <button type="submit">Register</button>
                </form>
                <p>Already have an account? <a href="#" onclick="showLogin()">Login here</a></p>
            </div>
        </div>
    </div>

    <!-- Product Modal -->
    <div id="product-modal" class="product-modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeProductModal()">&times;</button>
            <div class="modal-product-grid">
                <div class="modal-image-gallery">
                    <div class="image-slider-container">
                        <img id="modal-main-image" class="modal-main-image" src="" alt="">
                        <button class="slider-nav slider-prev" onclick="prevImage()">‹</button>
                        <button class="slider-nav slider-next" onclick="nextImage()">›</button>
                        <div class="image-counter">
                            <span id="current-image">1</span> / <span id="total-images">1</span>
                        </div>
                    </div>
                    <div id="image-dots" class="image-dots"></div>
                </div>
                <div class="modal-product-info">
                    <h2 id="modal-product-title" class="modal-product-title"></h2>
                    <div id="modal-product-price" class="modal-product-price"></div>
                    <div id="modal-product-rating" class="product-rating"></div>
                    <div id="modal-product-description" class="modal-product-description"></div>
                    <div class="modal-actions">
                        <button class="modal-btn primary" onclick="addToCartFromModal()">Add to Cart</button>
                        <button class="modal-btn secondary" onclick="addToWishlist()">Add to Wishlist</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Global functions for the new design
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('mobile-menu-icon');

            if (mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
                menuIcon.setAttribute('data-lucide', 'x');
            } else {
                mobileMenu.classList.add('hidden');
                menuIcon.setAttribute('data-lucide', 'menu');
            }
            lucide.createIcons();
        }

        function toggleUserMenu() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('hidden');
        }

        function handleSearchSubmit(event) {
            event.preventDefault();
            const searchInput = event.target.querySelector('input[type="text"]');
            const query = searchInput.value.trim();
            if (query) {
                window.location.href = `shop.html?search=${encodeURIComponent(query)}`;
            }
        }

        function openImageSearch() {
            document.getElementById('image-search-input').click();
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                // Implement image search functionality
                alert('Image search feature coming soon!');
            }
        }

        function handleNewsletterSubmit(event) {
            event.preventDefault();
            const email = document.getElementById('newsletter-email').value;
            if (email) {
                alert('Thank you for subscribing! You will receive our latest deals and updates.');
                document.getElementById('newsletter-email').value = '';
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('user-dropdown');
            const userMenuBtn = document.getElementById('user-menu-btn');

            if (!userMenuBtn.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // Animate stats on scroll
        function animateStats() {
            const stats = [
                { id: 'stat-products', target: 1000, suffix: '+' },
                { id: 'stat-customers', target: 50000, suffix: 'K+' },
                { id: 'stat-orders', target: 100000, suffix: 'K+' },
                { id: 'stat-rating', target: 4.9, suffix: '★' }
            ];

            stats.forEach(stat => {
                const element = document.getElementById(stat.id);
                if (element) {
                    let current = 0;
                    const increment = stat.target / 100;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= stat.target) {
                            current = stat.target;
                            clearInterval(timer);
                        }

                        if (stat.id === 'stat-rating') {
                            element.textContent = current.toFixed(1) + stat.suffix;
                        } else if (stat.suffix.includes('K')) {
                            element.textContent = Math.floor(current / 1000) + stat.suffix;
                        } else {
                            element.textContent = Math.floor(current) + stat.suffix;
                        }
                    }, 20);
                }
            });
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stats when they come into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateStats();
                        observer.unobserve(entry.target);
                    }
                });
            });

            const statsSection = document.querySelector('#stat-products')?.closest('section');
            if (statsSection) {
                observer.observe(statsSection);
            }
        });
    </script>

    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/woocommerce-api.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/products.js"></script>
    <script src="js/main.js"></script>
    <script src="sw-register.js"></script>
</body>
</html>
