# Deal4u - Static HTML/JavaScript E-commerce Website

A modern, responsive e-commerce website built with pure HTML, CSS, and JavaScript. This is a static version that connects directly to WooCommerce API for dynamic product data.

## 🚀 Features

### Core Features
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- **WooCommerce Integration** - Direct API connection to your WordPress/WooCommerce store
- **Smart Product Categorization** - Automatically categorizes products into Woman Clothes, Electronics, and Accessories
- **Real-time Product Sync** - Fetches live data from your WooCommerce store
- **Shopping Cart** - Full cart functionality with local storage
- **User Authentication** - Login/register system with demo accounts
- **Product Search** - Search through all products
- **Product Modals** - Detailed product view with image gallery
- **Admin Dashboard** - Complete admin panel for managing products and settings

### Advanced Features
- **PWA Support** - Progressive Web App with offline capabilities
- **Service Worker** - Caching and offline functionality
- **WhatsApp Integration** - Direct WhatsApp contact button
- **Newsletter Signup** - Email subscription functionality
- **Product Filtering** - Filter by category, price, and more
- **Auto-sync** - Automatic product synchronization
- **Cache Management** - Intelligent caching for better performance

## 📁 File Structure

```
Static-Deal4u-v2/
├── index.html              # Main homepage
├── shop.html               # Shop/products page
├── contact.html            # Contact page
├── admin.html              # Admin dashboard
├── styles.css              # Main stylesheet
├── manifest.json           # PWA manifest
├── sw.js                   # Service worker
├── sw-register.js          # Service worker registration
├── js/
│   ├── config.js           # Configuration settings
│   ├── utils.js            # Utility functions
│   ├── woocommerce-api.js  # WooCommerce API handler
│   ├── cart.js             # Shopping cart management
│   ├── auth.js             # Authentication system
│   ├── products.js         # Product management
│   ├── shop.js             # Shop page functionality
│   ├── admin.js            # Admin panel functionality
│   └── main.js             # Main application controller
└── README.md               # This file
```

## ⚙️ Setup Instructions

### 1. Configure WooCommerce API

Edit `js/config.js` and update the WooCommerce settings:

```javascript
WOOCOMMERCE: {
    BASE_URL: 'https://your-wordpress-site.com/wp-json/wc/v3',
    CONSUMER_KEY: 'ck_your_consumer_key_here',
    CONSUMER_SECRET: 'cs_your_consumer_secret_here'
}
```

### 2. Update Site Information

In `js/config.js`, update your site details:

```javascript
SITE: {
    NAME: 'Deal4u',
    DOMAIN: 'deal4u.co',
    EMAIL: '<EMAIL>',
    PHONE: '+447447186806',
    WHATSAPP: '447447186806'
}
```

### 3. Deploy to Web Server

Upload all files to your web server. The website works with any web server that can serve static files:

- **Apache** - Works out of the box
- **Nginx** - Works out of the box  
- **cPanel** - Upload to public_html folder
- **Netlify/Vercel** - Deploy directly from folder
- **GitHub Pages** - Push to repository and enable Pages

### 4. HTTPS Required

For PWA features and service worker to work, your site must be served over HTTPS.

## 🔧 Configuration Options

### WooCommerce API Setup

1. In your WordPress admin, go to WooCommerce → Settings → Advanced → REST API
2. Click "Add Key"
3. Set permissions to "Read"
4. Copy the Consumer Key and Consumer Secret
5. Update `js/config.js` with your credentials

### Smart Categorization

Products are automatically categorized based on keywords in their titles and descriptions:

- **Woman Clothes**: dress, blouse, skirt, pants, jeans, top, shirt, women, female, lady, girl
- **Electronics**: phone, laptop, computer, tablet, headphone, speaker, camera, tv, electronic, tech, gadget
- **Accessories**: bag, wallet, watch, jewelry, necklace, ring, bracelet, earring, sunglasses, belt, hat, scarf

You can modify these keywords in `js/config.js`.

## 👤 User Accounts

### Demo Accounts

The system includes demo accounts for testing:

- **Admin**: <EMAIL> / admin123
- **User**: <EMAIL> / user123
- **Test**: <EMAIL> / test123

### Admin Access

- Username: <EMAIL>
- Password: admin123
- Access the admin panel at `/admin.html`

## 🛠️ Admin Features

### Dashboard
- Product statistics
- System status monitoring
- Quick actions
- Sync logs

### Product Management
- View all products (including draft/private)
- Filter by status and category
- Search products
- Product details

### Synchronization
- Full sync (all products)
- Published products only
- Auto-sync toggle
- Sync logging

### Settings
- WooCommerce API configuration
- Site settings
- Cache management

## 🎨 Customization

### Styling

The website uses CSS custom properties for easy theming. Main colors can be changed in `styles.css`:

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #fbbf24;
    --text-color: #1f2937;
    --background-color: #ffffff;
}
```

### Branding

Update branding elements in `js/config.js`:

```javascript
SITE: {
    NAME: 'Your Store Name',
    DOMAIN: 'yourstore.com',
    EMAIL: '<EMAIL>',
    PHONE: '+1234567890'
}
```

## 📱 PWA Features

The website includes Progressive Web App features:

- **Installable** - Users can install it as an app
- **Offline Support** - Basic functionality works offline
- **Push Notifications** - Ready for push notification setup
- **App-like Experience** - Feels like a native mobile app

## 🔒 Security Notes

- API credentials are stored in client-side JavaScript (visible to users)
- For production, consider using a server-side proxy for API calls
- The admin system is demo-only and not suitable for production without additional security
- Always use HTTPS in production

## 🚀 Performance

### Optimization Features
- **Lazy Loading** - Images load as needed
- **Caching** - Intelligent caching system
- **Minification** - CSS and JS can be minified for production
- **CDN Ready** - All assets can be served from CDN

### Loading Speed
- **First Load** - ~2-3 seconds (depending on API response)
- **Cached Load** - ~0.5-1 second
- **Offline Load** - Instant (cached content)

## 🐛 Troubleshooting

### Common Issues

1. **Products not loading**
   - Check WooCommerce API credentials
   - Verify API endpoint URL
   - Check browser console for errors

2. **CORS errors**
   - Ensure your WordPress site allows cross-origin requests
   - Consider using a server-side proxy

3. **PWA not working**
   - Ensure site is served over HTTPS
   - Check service worker registration

4. **Admin access denied**
   - Use demo credentials: <EMAIL> / admin123
   - Check browser console for errors

## 📞 Support

For support and questions:

- **Email**: <EMAIL>
- **Phone**: +447447186806
- **WhatsApp**: +447447186806

## 📄 License

This project is provided as-is for educational and commercial use. Feel free to modify and customize according to your needs.

---

**Deal4u** - Premium Products, Amazing Deals! 🛍️
