// Main Application Logic
class Deal4uApp {
    constructor() {
        this.currentPage = this.getCurrentPage();
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadPageContent();
        this.setupBackToTop();
        this.setupLazyLoading();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html') return 'home';
        if (path.includes('shop')) return 'shop';
        if (path.includes('product')) return 'product';
        if (path.includes('category')) return 'category';
        return 'home';
    }

    setupEventListeners() {
        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#user-menu') && !e.target.closest('button[onclick="toggleUserMenu()"]')) {
                const userMenu = document.getElementById('user-menu');
                if (userMenu) userMenu.classList.add('hidden');
            }

            if (!e.target.closest('#search-results') && !e.target.closest('#search-input')) {
                const searchResults = document.getElementById('search-results');
                if (searchResults) searchResults.classList.add('hidden');
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const cartSidebar = document.getElementById('cart-sidebar');
                if (cartSidebar && !cartSidebar.classList.contains('hidden')) {
                    toggleCart();
                }
            }
        });
    }

    async loadPageContent() {
        switch (this.currentPage) {
            case 'home':
                await this.loadHomePage();
                break;
            case 'shop':
                await this.loadShopPage();
                break;
            default:
                await this.loadHomePage();
        }
    }

    async loadHomePage() {
        try {
            // Disable main.js product loading to prevent conflicts with inline system
            console.log('Home page loading disabled - using inline product loading system');
            // The inline JavaScript in index.html will handle product loading
        } catch (error) {
            console.error('Error loading home page:', error);
        }
    }

    async loadShopPage() {
        try {
            const result = await window.wooAPI.getProducts({ per_page: 20 });
            if (result.success) {
                this.renderProducts(result.data, 'products-grid');
            } else {
                this.showError('products-grid', 'Failed to load products');
            }
        } catch (error) {
            console.error('Error loading shop page:', error);
            this.showError('products-grid', 'Error loading products');
        }
    }

    renderProducts(products, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!products || products.length === 0) {
            container.innerHTML = `
                <div class="loading">
                    <p style="color: #6b7280;">No products found</p>
                </div>
            `;
            return;
        }

        // Process products and generate HTML using the existing style
        const productsHTML = products.map(product => {
            const processedProduct = window.wooAPI.processProductData(product);

            return `
                <div class="product-card" onclick="openProductModal(${product.id})">
                    <div class="product-image">
                        <img src="${processedProduct.image}" alt="${processedProduct.name}" loading="lazy">
                        ${processedProduct.discount_percentage > 0 ? `
                            <div class="product-badge">${processedProduct.discount_percentage}% OFF</div>
                        ` : ''}
                        <div class="product-quick-actions">
                            <button class="quick-action-btn" title="Add to Wishlist" onclick="event.stopPropagation();">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="quick-action-btn" title="Quick View" onclick="event.stopPropagation(); openProductModal(${product.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="quick-action-btn" title="Share" onclick="event.stopPropagation();">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                        <button class="image-search-btn" onclick="event.stopPropagation(); openImageSearchModal('${processedProduct.image}')">
                            <i class="fas fa-search"></i> Find Similar
                        </button>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">${processedProduct.name}</h3>
                        <div class="product-rating">
                            <div class="stars">★★★★★</div>
                            <span class="rating-text">(${Math.floor(Math.random() * 100) + 10})</span>
                        </div>
                        <div class="product-features">
                            <span class="feature-tag">Quality</span>
                            <span class="feature-tag">Fast Ship</span>
                        </div>
                        <div class="product-price">
                            ${processedProduct.sale_price ? `
                                <span class="current-price">${processedProduct.formatted_sale_price}</span>
                                <span class="original-price">${processedProduct.formatted_regular_price}</span>
                                <span class="savings">Save ${processedProduct.discount_percentage}%</span>
                            ` : `
                                <span class="current-price">${processedProduct.formatted_price}</span>
                            `}
                        </div>
                        <button class="add-to-cart" onclick="event.stopPropagation(); addToCart(${JSON.stringify(processedProduct).replace(/"/g, '&quot;')})">
                            <i class="fas fa-cart-plus"></i> Add to Cart
                        </button>
                    </div>
                    <div class="product-card-overlay">
                        <div class="overlay-buttons">
                            <button class="overlay-btn primary" onclick="event.stopPropagation(); addToCart(${JSON.stringify(processedProduct).replace(/"/g, '&quot;')})">
                                <i class="fas fa-cart-plus"></i> Add to Cart
                            </button>
                            <button class="overlay-btn" onclick="event.stopPropagation(); openProductModal(${product.id})">
                                <i class="fas fa-eye"></i> Quick View
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = productsHTML;
    }

    showError(containerId, message) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="loading">
                    <p style="color: #ef4444;">${message}</p>
                    <p style="font-size: 14px; color: #6b7280;">Please check your internet connection and try again.</p>
                    <button onclick="location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #2563eb; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
                        Try Again
                    </button>
                </div>
            `;
        }
    }

    setupBackToTop() {
        const backToTopBtn = document.getElementById('back-to-top');
        if (!backToTopBtn) return;

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('hidden');
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.add('hidden');
                backToTopBtn.classList.remove('visible');
            }
        });

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }
}

// Search functionality
async function handleSearch(event) {
    const query = event.target.value.trim();
    const resultsContainer = document.getElementById('search-results');
    
    if (!resultsContainer) return;

    if (query.length < 2) {
        resultsContainer.classList.add('hidden');
        return;
    }

    // Clear previous timeout
    if (window.searchTimeout) {
        clearTimeout(window.searchTimeout);
    }

    // Debounce search
    window.searchTimeout = setTimeout(async () => {
        try {
            showLoadingIndicator();
            const result = await window.wooAPI.searchProducts(query, 5);
            hideLoadingIndicator();

            if (result.success && result.data.length > 0) {
                resultsContainer.innerHTML = result.data.map(product => `
                    <div class="search-result-item" onclick="goToProduct(${product.id})">
                        <div class="flex items-center space-x-3">
                            <img src="${product.images[0]?.src || '/placeholder-product.jpg'}" 
                                 alt="${product.name}" class="w-12 h-12 object-cover rounded">
                            <div class="flex-1">
                                <h4 class="font-medium text-sm line-clamp-1">${product.name}</h4>
                                <p class="text-blue-600 font-semibold text-sm">${window.wooAPI.formatPrice(product.price)}</p>
                            </div>
                        </div>
                    </div>
                `).join('');
                resultsContainer.classList.remove('hidden');
            } else {
                resultsContainer.innerHTML = `
                    <div class="search-result-item">
                        <p class="text-gray-500 text-center">No products found</p>
                    </div>
                `;
                resultsContainer.classList.remove('hidden');
            }
        } catch (error) {
            hideLoadingIndicator();
            console.error('Search error:', error);
        }
    }, 300);
}

// Utility functions
function showLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.classList.remove('hidden');
    }
}

function hideLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.classList.add('hidden');
    }
}

function scrollToProducts() {
    const productsSection = document.getElementById('featured-products');
    if (productsSection) {
        productsSection.scrollIntoView({ behavior: 'smooth' });
    }
}

function goToProduct(productId) {
    // Use the modal preview instead of redirecting to a non-existent page
    if (typeof openProductPreview === 'function') {
        openProductPreview(productId);
    } else {
        console.log('Product preview modal not available, product ID:', productId);
    }
}

function openProductModal(productId) {
    // Bridge function to connect main.js with the modal system
    goToProduct(productId);
}

// Toast notification system
function showToast(message, type = 'info') {
    const container = document.getElementById('toast-container');
    if (!container) return;

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const icon = type === 'success' ? 'check-circle' : 
                 type === 'error' ? 'x-circle' : 'info';
    
    toast.innerHTML = `
        <i data-lucide="${icon}" class="w-5 h-5"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()" class="ml-auto">
            <i data-lucide="x" class="w-4 h-4"></i>
        </button>
    `;

    container.appendChild(toast);

    // Re-initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new Deal4uApp();
});
