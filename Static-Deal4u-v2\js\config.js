// Deal4u Configuration
const CONFIG = {
    // WooCommerce API Configuration
    WOOCOMMERCE: {
        BASE_URL: 'https://deal4u.co/wp-json/wc/v3',
        CONSUMER_KEY: 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193',
        CONSUMER_SECRET: 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3',
        // Alternative: Use your API proxy if you have one
        USE_PROXY: false,
        PROXY_URL: '/api/woocommerce'
    },

    // Site Configuration
    SITE: {
        NAME: 'Deal4u',
        DOMAIN: 'deal4u.co',
        EMAIL: '<EMAIL>',
        PHONE: '+447447186806',
        WHATSAPP: '447447186806',
        CURRENCY: '£',
        CURRENCY_CODE: 'GBP'
    },

    // Product Configuration
    PRODUCTS: {
        PER_PAGE: 20,
        MAX_PRODUCTS: 100,
        DEFAULT_IMAGE: 'placeholder.jpg',
        ENABLE_REVIEWS: true,
        ENABLE_WISHLIST: true,
        ENABLE_COMPARE: false
    },

    // Smart Categorization
    CATEGORIES: {
        'woman-clothes': {
            name: 'Woman Clothes',
            keywords: ['dress', 'blouse', 'skirt', 'pants', 'jeans', 'top', 'shirt', 'women', 'female', 'lady', 'girl'],
            icon: 'fas fa-female'
        },
        'electronics': {
            name: 'Electronics',
            keywords: ['phone', 'laptop', 'computer', 'tablet', 'headphone', 'speaker', 'camera', 'tv', 'electronic', 'tech', 'gadget'],
            icon: 'fas fa-mobile-alt'
        },
        'accessories': {
            name: 'Accessories',
            keywords: ['bag', 'wallet', 'watch', 'jewelry', 'necklace', 'ring', 'bracelet', 'earring', 'sunglasses', 'belt', 'hat', 'scarf'],
            icon: 'fas fa-gem'
        }
    },

    // Cart Configuration
    CART: {
        STORAGE_KEY: 'deal4u_cart',
        MAX_QUANTITY: 10,
        ENABLE_GUEST_CHECKOUT: true,
        SHIPPING_THRESHOLD: 50 // Free shipping over £50
    },

    // Authentication
    AUTH: {
        STORAGE_KEY: 'deal4u_user',
        ENABLE_SOCIAL_LOGIN: false,
        REQUIRE_EMAIL_VERIFICATION: false
    },

    // UI Configuration
    UI: {
        THEME: 'default',
        ENABLE_DARK_MODE: false,
        ANIMATION_DURATION: 300,
        TOAST_DURATION: 3000,
        ENABLE_PWA: true
    },

    // API Endpoints
    ENDPOINTS: {
        PRODUCTS: '/products',
        CATEGORIES: '/products/categories',
        ORDERS: '/orders',
        CUSTOMERS: '/customers',
        COUPONS: '/coupons'
    },

    // Feature Flags
    FEATURES: {
        SMART_CATEGORIZATION: true,
        PRODUCT_REVIEWS: true,
        WISHLIST: true,
        COMPARE_PRODUCTS: false,
        LIVE_CHAT: false,
        NEWSLETTER: true,
        SOCIAL_SHARING: true,
        PRODUCT_ZOOM: true,
        INFINITE_SCROLL: false,
        LAZY_LOADING: true
    },

    // Admin Configuration
    ADMIN: {
        USERNAME: 'admin',
        PASSWORD: 'admin', // Change this in production!
        ENABLE_ANALYTICS: true,
        ENABLE_PRODUCT_MANAGEMENT: true
    },

    // Development/Debug
    DEBUG: {
        ENABLED: true, // Set to true for development
        LOG_API_CALLS: true,
        SHOW_CONSOLE_LOGS: true,
        USE_MOCK_DATA: false // Disable demo data to use real WooCommerce API
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// Make available globally
window.CONFIG = CONFIG;

// Helper function to get nested config values
function getConfig(path, defaultValue = null) {
    return path.split('.').reduce((obj, key) => obj && obj[key], CONFIG) || defaultValue;
}

// Helper function to check if feature is enabled
function isFeatureEnabled(feature) {
    return getConfig(`FEATURES.${feature}`, false);
}

// Helper function to get API URL
function getApiUrl(endpoint = '') {
    if (CONFIG.WOOCOMMERCE.USE_PROXY) {
        return CONFIG.WOOCOMMERCE.PROXY_URL + endpoint;
    }
    return CONFIG.WOOCOMMERCE.BASE_URL + endpoint;
}

// Helper function to format currency
function formatCurrency(amount) {
    return `${CONFIG.SITE.CURRENCY}${parseFloat(amount).toFixed(2)}`;
}

// Helper function to get category by keywords
function getCategoryByKeywords(title, description = '') {
    const text = (title + ' ' + description).toLowerCase();
    
    for (const [key, category] of Object.entries(CONFIG.CATEGORIES)) {
        if (category.keywords.some(keyword => text.includes(keyword))) {
            return key;
        }
    }
    
    return 'accessories'; // Default category
}

// Initialize configuration
console.log('Deal4u Configuration Loaded:', CONFIG.SITE.NAME);
