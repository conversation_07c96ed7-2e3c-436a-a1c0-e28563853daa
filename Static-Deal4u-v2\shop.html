<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop - Deal4u</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#667eea">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="/" class="logo">
                <div class="logo-icon">D</div>
                <span>Deal4u</span>
            </a>

            <nav class="nav-menu">
                <a href="index.html">Home</a>
                <a href="#" class="active">Shop</a>
                <a href="index.html#featured">Products</a>
                <a href="index.html#why-choose">About</a>
                <a href="contact.html">Contact</a>
                <a href="admin.html" id="admin-link" style="display: none;">Admin</a>
            </nav>

            <div class="header-actions">
                <div class="search-box">
                    <input type="text" placeholder="Search products..." id="search-input">
                    <button onclick="searchProducts()"><i class="fas fa-search"></i></button>
                </div>
                <button class="cart-btn" onclick="toggleCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Cart</span>
                    <span class="cart-count" id="cart-count">0</span>
                </button>
                <button class="auth-btn" id="auth-btn" onclick="toggleAuth()">
                    <i class="fas fa-user"></i>
                    <span id="auth-text">Login</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Shop Hero -->
    <section class="shop-hero">
        <div class="shop-hero-container">
            <h1>Shop All Products</h1>
            <p>Discover our complete collection of premium products at amazing prices</p>
            <div class="shop-stats">
                <div class="stat">
                    <div class="stat-number" id="total-products">0</div>
                    <div class="stat-label">Products Available</div>
                </div>
                <div class="stat">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Categories</div>
                </div>
                <div class="stat">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Brands</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Shop Filters -->
    <section class="shop-filters">
        <div class="container">
            <div class="filters-header">
                <h2>Filter Products</h2>
                <button class="clear-filters" onclick="clearAllFilters()">Clear All</button>
            </div>
            
            <div class="filters-grid">
                <div class="filter-group">
                    <h3>Categories</h3>
                    <div class="category-filters">
                        <button class="filter-btn active" data-category="all">All Products</button>
                        <button class="filter-btn" data-category="woman-clothes">Woman Clothes</button>
                        <button class="filter-btn" data-category="electronics">Electronics</button>
                        <button class="filter-btn" data-category="accessories">Accessories</button>
                    </div>
                </div>

                <div class="filter-group">
                    <h3>Price Range</h3>
                    <div class="price-filters">
                        <button class="filter-btn" data-price="0-25">£0 - £25</button>
                        <button class="filter-btn" data-price="25-50">£25 - £50</button>
                        <button class="filter-btn" data-price="50-100">£50 - £100</button>
                        <button class="filter-btn" data-price="100+">£100+</button>
                    </div>
                </div>

                <div class="filter-group">
                    <h3>Sort By</h3>
                    <select class="sort-select" id="sort-select">
                        <option value="default">Default</option>
                        <option value="price-low">Price: Low to High</option>
                        <option value="price-high">Price: High to Low</option>
                        <option value="name">Name: A to Z</option>
                        <option value="newest">Newest First</option>
                        <option value="featured">Featured</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Grid -->
    <section class="shop-products">
        <div class="container">
            <div class="products-header">
                <h2>All Products</h2>
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid" title="Grid View">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="list" title="List View">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>

            <div id="shop-products-grid" class="products-grid">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading all products...</p>
                </div>
            </div>

            <div class="load-more-container">
                <button id="load-more-btn" class="btn-primary" style="display: none;">Load More Products</button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Deal4u</h3>
                    <p>Your trusted partner for premium products at amazing deals.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="about.html">About</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Customer Service</h3>
                    <ul>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                        <li><a href="returns.html">Returns</a></li>
                        <li><a href="shipping.html">Shipping</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +447447186806</p>
                        <p><i class="fas fa-map-marker-alt"></i> London, UK</p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 Deal4u. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Button -->
    <a href="https://wa.me/447447186806?text=Hello! I'm interested in your products." class="whatsapp-btn" id="whatsapp-btn">
        <i class="fab fa-whatsapp whatsapp-icon"></i>
    </a>

    <!-- Cart Sidebar -->
    <div id="cart-sidebar" class="cart-sidebar">
        <div class="cart-header">
            <h3>Shopping Cart</h3>
            <button onclick="toggleCart()" class="close-cart">&times;</button>
        </div>
        <div id="cart-items" class="cart-items">
            <p class="empty-cart">Your cart is empty</p>
        </div>
        <div class="cart-footer">
            <div class="cart-total">
                <strong>Total: £<span id="cart-total">0.00</span></strong>
            </div>
            <button class="checkout-btn" onclick="proceedToCheckout()">Proceed to Checkout</button>
        </div>
    </div>

    <!-- Auth Modal -->
    <div id="auth-modal" class="auth-modal">
        <div class="auth-content">
            <button class="auth-close" onclick="toggleAuth()">&times;</button>
            <div id="login-form" class="auth-form">
                <h2>Login to Deal4u</h2>
                <form onsubmit="handleLogin(event)">
                    <input type="email" placeholder="Email" required>
                    <input type="password" placeholder="Password" required>
                    <button type="submit">Login</button>
                </form>
                <p>Don't have an account? <a href="#" onclick="showRegister()">Register here</a></p>
            </div>
            <div id="register-form" class="auth-form" style="display: none;">
                <h2>Register for Deal4u</h2>
                <form onsubmit="handleRegister(event)">
                    <input type="text" placeholder="Full Name" required>
                    <input type="email" placeholder="Email" required>
                    <input type="password" placeholder="Password" required>
                    <input type="password" placeholder="Confirm Password" required>
                    <button type="submit">Register</button>
                </form>
                <p>Already have an account? <a href="#" onclick="showLogin()">Login here</a></p>
            </div>
        </div>
    </div>

    <!-- Product Modal -->
    <div id="product-modal" class="product-modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeProductModal()">&times;</button>
            <div class="modal-product-grid">
                <div class="modal-image-gallery">
                    <div class="image-slider-container">
                        <img id="modal-main-image" class="modal-main-image" src="" alt="">
                        <button class="slider-nav slider-prev" onclick="prevImage()">‹</button>
                        <button class="slider-nav slider-next" onclick="nextImage()">›</button>
                        <div class="image-counter">
                            <span id="current-image">1</span> / <span id="total-images">1</span>
                        </div>
                    </div>
                    <div id="image-dots" class="image-dots"></div>
                </div>
                <div class="modal-product-info">
                    <h2 id="modal-product-title" class="modal-product-title"></h2>
                    <div id="modal-product-price" class="modal-product-price"></div>
                    <div id="modal-product-rating" class="product-rating"></div>
                    <div id="modal-product-description" class="modal-product-description"></div>
                    <div class="modal-actions">
                        <button class="modal-btn primary" onclick="addToCartFromModal()">Add to Cart</button>
                        <button class="modal-btn secondary" onclick="addToWishlist()">Add to Wishlist</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/woocommerce-api.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/products.js"></script>
    <script src="js/shop.js"></script>
    <script src="js/main.js"></script>
    <script src="sw-register.js"></script>
</body>
</html>
