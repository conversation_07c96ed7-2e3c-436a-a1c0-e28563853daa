<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop - Deal4u</title>
    <meta name="description" content="Browse our wide selection of premium products at amazing prices. Find electronics, fashion, home goods, and more.">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- PWA -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#3b82f6">
    <link rel="apple-touch-icon" href="icons/icon-192x192.png">
</head>
<body class="font-inter bg-gray-50">
    <!-- Header -->
    <header id="header" class="sticky top-0 z-50 transition-all duration-300 bg-white shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">D</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Deal4u</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-1">
                    <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Home</a>
                    <a href="shop.html" class="px-3 py-2 rounded-md text-sm font-medium text-blue-600 bg-blue-50 transition-colors">Shop</a>
                    <a href="categories.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Categories</a>
                    <a href="track-order.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Track Order</a>
                    <a href="about.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">About</a>
                    <a href="contact.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Contact</a>
                    <a href="faq.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">FAQ</a>
                </nav>

                <!-- Search and Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Search -->
                    <form onsubmit="handleSearchSubmit(event)" class="hidden md:block">
                        <div class="relative">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                            <input
                                type="text"
                                placeholder="Search products..."
                                id="search-input"
                                oninput="handleSearchInput(this)"
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                            />
                            <button
                                type="button"
                                onclick="openImageSearch()"
                                class="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600 transition-colors"
                                title="Search by image"
                            >
                                <i data-lucide="camera" class="w-5 h-5"></i>
                            </button>
                            <input
                                type="file"
                                accept="image/*"
                                class="hidden"
                                id="image-search-input"
                                onchange="handleImageUpload(event)"
                            />
                            <button
                                type="submit"
                                id="search-submit-btn"
                                class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 hidden"
                            >
                                Search
                            </button>
                        </div>
                    </form>

                    <!-- Wishlist -->
                    <button onclick="toggleWishlist()" class="relative p-2 text-gray-600 hover:text-red-500 transition-colors">
                        <i data-lucide="heart" class="w-6 h-6"></i>
                        <span id="wishlist-count" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                    </button>

                    <!-- Cart -->
                    <button onclick="toggleCart()" class="relative p-2 text-gray-600 hover:text-blue-600 transition-colors">
                        <i data-lucide="shopping-cart" class="w-6 h-6"></i>
                        <span id="cart-count" class="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                    </button>

                    <!-- User Menu -->
                    <div class="relative">
                        <button onclick="toggleUserMenu()" class="flex items-center space-x-2 p-2 text-gray-600 hover:text-blue-600 transition-colors">
                            <i data-lucide="user" class="w-6 h-6"></i>
                            <span class="hidden md:block text-sm font-medium" id="user-name">Account</span>
                            <i data-lucide="chevron-down" class="w-4 h-4"></i>
                        </button>

                        <!-- User Dropdown -->
                        <div id="user-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <div id="user-menu-authenticated" class="hidden">
                                <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Profile</a>
                                <a href="orders.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Orders</a>
                                <a href="wishlist.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Wishlist</a>
                                <a href="settings.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                                <div class="border-t border-gray-100"></div>
                                <button onclick="logout()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</button>
                            </div>
                            <div id="user-menu-guest">
                                <a href="login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign in</a>
                                <a href="register.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Create account</a>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <button onclick="toggleMobileMenu()" class="md:hidden p-2 text-gray-600 hover:text-blue-600 transition-colors">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden bg-white border-t border-gray-200 hidden">
            <div class="px-4 py-4 space-y-4">
                <!-- Mobile Search -->
                <form onsubmit="handleSearchSubmit(event)" class="relative">
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                    <input
                        type="text"
                        placeholder="Search products..."
                        id="mobile-search-input"
                        oninput="handleSearchInput(this)"
                        class="w-full pl-10 pr-16 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <button
                        type="button"
                        onclick="openImageSearch()"
                        class="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600 transition-colors"
                        title="Search by image"
                    >
                        <i data-lucide="camera" class="w-5 h-5"></i>
                    </button>
                    <button
                        type="submit"
                        id="mobile-search-submit-btn"
                        class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 hidden"
                    >
                        Search
                    </button>
                </form>

                <!-- Mobile Navigation -->
                <div class="space-y-1">
                    <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Home</a>
                    <a href="shop.html" class="block px-3 py-2 rounded-md text-base font-medium text-blue-600 bg-blue-50 transition-colors">Shop</a>
                    <a href="categories.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Categories</a>
                    <a href="track-order.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Track Order</a>
                    <a href="about.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">About</a>
                    <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Contact</a>
                    <a href="faq.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">FAQ</a>
                    <a href="wishlist.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-red-500 hover:bg-gray-50 transition-colors">Wishlist</a>
                    <a href="register.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Register</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="min-h-screen bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col md:flex-row gap-8">
                <!-- Filters Sidebar -->
                <div class="w-full md:w-64 space-y-6">
                    <!-- Mobile Filter Toggle -->
                    <div class="lg:hidden mb-6">
                        <button
                            onclick="toggleFilters()"
                            id="filter-toggle"
                            class="flex items-center justify-between w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50"
                        >
                            <span class="flex items-center">
                                <i data-lucide="filter" class="w-4 h-4 mr-2"></i>
                                Filters
                                <span id="active-filters-badge" class="ml-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full hidden">
                                    Active
                                </span>
                            </span>
                            <i data-lucide="chevron-down" class="w-4 h-4" id="filter-chevron"></i>
                        </button>
                    </div>

                    <!-- Filter Panel -->
                    <div id="filter-panel" class="hidden lg:block bg-white rounded-lg shadow-md p-6">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-semibold text-gray-900">Filters</h2>
                            <button
                                onclick="clearAllFilters()"
                                id="clear-filters-btn"
                                class="text-sm text-blue-600 hover:text-blue-700 flex items-center hidden"
                            >
                                <i data-lucide="x" class="w-4 h-4 mr-1"></i>
                                Clear All
                            </button>
                        </div>

                        <!-- Categories -->
                        <div class="border-b border-gray-200 pb-6 mb-6">
                            <button
                                onclick="toggleFilterSection('categories')"
                                class="flex items-center justify-between w-full text-left"
                            >
                                <h3 class="text-lg font-medium text-gray-900">Categories</h3>
                                <i data-lucide="chevron-up" class="w-5 h-5 text-gray-500" id="categories-chevron"></i>
                            </button>
                            <div id="categories-section" class="mt-4">
                                <div class="space-y-3">
                                    <label class="flex items-center">
                                        <input
                                            type="radio"
                                            name="category"
                                            value="all"
                                            checked
                                            onchange="handleCategoryFilter(this.value)"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                        />
                                        <span class="ml-3 text-sm text-gray-700">All Categories</span>
                                    </label>
                                    <div id="category-filters">
                                        <!-- Categories will be loaded dynamically -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Price Range -->
                        <div class="border-b border-gray-200 pb-6 mb-6">
                            <button
                                onclick="toggleFilterSection('price')"
                                class="flex items-center justify-between w-full text-left"
                            >
                                <h3 class="text-lg font-medium text-gray-900">Price Range</h3>
                                <i data-lucide="chevron-up" class="w-5 h-5 text-gray-500" id="price-chevron"></i>
                            </button>
                            <div id="price-section" class="mt-4">
                                <div class="space-y-4">
                                    <!-- Quick Price Ranges -->
                                    <div class="space-y-2">
                                        <button
                                            onclick="handlePriceRange(0, 25)"
                                            class="block w-full text-left px-3 py-2 text-sm rounded-md transition-colors text-gray-700 hover:bg-gray-100"
                                        >
                                            Under £25
                                        </button>
                                        <button
                                            onclick="handlePriceRange(25, 50)"
                                            class="block w-full text-left px-3 py-2 text-sm rounded-md transition-colors text-gray-700 hover:bg-gray-100"
                                        >
                                            £25 - £50
                                        </button>
                                        <button
                                            onclick="handlePriceRange(50, 100)"
                                            class="block w-full text-left px-3 py-2 text-sm rounded-md transition-colors text-gray-700 hover:bg-gray-100"
                                        >
                                            £50 - £100
                                        </button>
                                        <button
                                            onclick="handlePriceRange(100, 200)"
                                            class="block w-full text-left px-3 py-2 text-sm rounded-md transition-colors text-gray-700 hover:bg-gray-100"
                                        >
                                            £100 - £200
                                        </button>
                                        <button
                                            onclick="handlePriceRange(200, '')"
                                            class="block w-full text-left px-3 py-2 text-sm rounded-md transition-colors text-gray-700 hover:bg-gray-100"
                                        >
                                            Over £200
                                        </button>
                                    </div>

                                    <!-- Custom Price Range -->
                                    <div class="border-t pt-4">
                                        <p class="text-sm font-medium text-gray-700 mb-3">Custom Range</p>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-full">
                                                <input
                                                    type="number"
                                                    placeholder="Min"
                                                    id="min-price"
                                                    onchange="handleCustomPriceRange()"
                                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                />
                                            </div>
                                            <span class="text-gray-500">-</span>
                                            <div class="w-full">
                                                <input
                                                    type="number"
                                                    placeholder="Max"
                                                    id="max-price"
                                                    onchange="handleCustomPriceRange()"
                                                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Rating -->
                        <div class="border-b border-gray-200 pb-6 mb-6">
                            <button
                                onclick="toggleFilterSection('rating')"
                                class="flex items-center justify-between w-full text-left"
                            >
                                <h3 class="text-lg font-medium text-gray-900">Customer Rating</h3>
                                <i data-lucide="chevron-up" class="w-5 h-5 text-gray-500" id="rating-chevron"></i>
                            </button>
                            <div id="rating-section" class="mt-4">
                                <div class="space-y-3">
                                    <label class="flex items-center">
                                        <input
                                            type="radio"
                                            name="rating"
                                            value=""
                                            checked
                                            onchange="handleRatingFilter(this.value)"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                        />
                                        <span class="ml-3 text-sm text-gray-700">All Ratings</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input
                                            type="radio"
                                            name="rating"
                                            value="4"
                                            onchange="handleRatingFilter(this.value)"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                        />
                                        <span class="ml-3 text-sm text-gray-700 flex items-center">
                                            4 stars & up
                                            <div class="ml-2 flex">
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                                <i data-lucide="star" class="w-4 h-4 text-gray-300"></i>
                                            </div>
                                        </span>
                                    </label>

                                    <label class="flex items-center">
                                        <input
                                            type="radio"
                                            name="rating"
                                            value="3"
                                            onchange="handleRatingFilter(this.value)"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                        />
                                        <span class="ml-3 text-sm text-gray-700 flex items-center">
                                            3 stars & up
                                            <div class="ml-2 flex">
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                                <i data-lucide="star" class="w-4 h-4 text-gray-300"></i>
                                                <i data-lucide="star" class="w-4 h-4 text-gray-300"></i>
                                            </div>
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Availability -->
                        <div>
                            <button
                                onclick="toggleFilterSection('availability')"
                                class="flex items-center justify-between w-full text-left"
                            >
                                <h3 class="text-lg font-medium text-gray-900">Availability</h3>
                                <i data-lucide="chevron-up" class="w-5 h-5 text-gray-500" id="availability-chevron"></i>
                            </button>
                            <div id="availability-section" class="mt-4">
                                <div class="space-y-3">
                                    <label class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="in-stock-filter"
                                            onchange="handleAvailabilityFilter()"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        />
                                        <span class="ml-3 text-sm text-gray-700">In Stock Only</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="on-sale-filter"
                                            onchange="handleAvailabilityFilter()"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        />
                                        <span class="ml-3 text-sm text-gray-700">On Sale</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Grid -->
                <div class="flex-1">
                    <!-- Loading State -->
                    <div id="loading-state" class="text-center py-12">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-4 text-gray-600">Loading products...</p>
                    </div>

                    <!-- No Products State -->
                    <div id="no-products-state" class="w-full py-12 flex flex-col items-center justify-center bg-white rounded-lg shadow-sm hidden">
                        <i data-lucide="shopping-cart" class="w-16 h-16 text-gray-400 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No Products Available</h3>
                        <p class="text-gray-500 text-center max-w-md mb-6">
                            Please check your WooCommerce store configuration or add products to your store.
                        </p>
                        <div class="mt-6">
                            <a
                                href="contact.html"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                Contact Support
                                <i data-lucide="chevron-right" class="ml-2 h-4 w-4"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Products Content -->
                    <div id="products-content" class="hidden">
                        <!-- Header with product count and view toggle -->
                        <div class="flex justify-between items-center mb-6">
                            <div class="text-sm text-gray-600" id="product-count">
                                Showing 1-24 of 0 products
                            </div>
                            <div class="flex items-center space-x-4">
                                <!-- Sort Dropdown -->
                                <select id="sort-select" onchange="handleSortChange()" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="default">Default</option>
                                    <option value="price-low">Price: Low to High</option>
                                    <option value="price-high">Price: High to Low</option>
                                    <option value="rating">Rating</option>
                                    <option value="popularity">Popularity</option>
                                    <option value="date">Newest</option>
                                </select>

                                <!-- View Toggle -->
                                <div class="inline-flex rounded-lg border border-gray-200 p-1">
                                    <button
                                        onclick="setViewMode('grid')"
                                        id="grid-view-btn"
                                        class="p-2 rounded-md transition-colors bg-blue-600 text-white"
                                        aria-label="Grid view"
                                    >
                                        <i data-lucide="grid-3x3" class="w-5 h-5"></i>
                                    </button>
                                    <button
                                        onclick="setViewMode('list')"
                                        id="list-view-btn"
                                        class="p-2 rounded-md transition-colors text-gray-600 hover:bg-gray-100"
                                        aria-label="List view"
                                    >
                                        <i data-lucide="list" class="w-5 h-5"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Products Grid/List -->
                        <div id="products-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            <!-- Products will be loaded here -->
                        </div>

                        <!-- Pagination -->
                        <div id="pagination" class="flex justify-center items-center mt-8 space-x-2 hidden">
                            <!-- Pagination will be generated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">D</span>
                        </div>
                        <span class="text-xl font-bold">Deal4u</span>
                    </div>
                    <p class="text-gray-300 text-sm leading-relaxed">
                        Your trusted partner for premium products at amazing deals. We bring you the best quality items at unbeatable prices.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="facebook" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="twitter" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="instagram" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="youtube" class="w-5 h-5"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-300 hover:text-white transition-colors text-sm">Home</a></li>
                        <li><a href="shop.html" class="text-gray-300 hover:text-white transition-colors text-sm">Shop</a></li>
                        <li><a href="categories.html" class="text-gray-300 hover:text-white transition-colors text-sm">Categories</a></li>
                        <li><a href="about.html" class="text-gray-300 hover:text-white transition-colors text-sm">About Us</a></li>
                        <li><a href="contact.html" class="text-gray-300 hover:text-white transition-colors text-sm">Contact</a></li>
                        <li><a href="faq.html" class="text-gray-300 hover:text-white transition-colors text-sm">FAQ</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Customer Service</h3>
                    <ul class="space-y-2">
                        <li><a href="track-order.html" class="text-gray-300 hover:text-white transition-colors text-sm">Track Order</a></li>
                        <li><a href="returns.html" class="text-gray-300 hover:text-white transition-colors text-sm">Returns & Exchanges</a></li>
                        <li><a href="shipping.html" class="text-gray-300 hover:text-white transition-colors text-sm">Shipping Info</a></li>
                        <li><a href="size-guide.html" class="text-gray-300 hover:text-white transition-colors text-sm">Size Guide</a></li>
                        <li><a href="privacy.html" class="text-gray-300 hover:text-white transition-colors text-sm">Privacy Policy</a></li>
                        <li><a href="terms.html" class="text-gray-300 hover:text-white transition-colors text-sm">Terms of Service</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="mail" class="w-5 h-5 text-blue-400"></i>
                            <span class="text-gray-300 text-sm"><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i data-lucide="phone" class="w-5 h-5 text-blue-400"></i>
                            <span class="text-gray-300 text-sm">+447447186806</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i data-lucide="map-pin" class="w-5 h-5 text-blue-400"></i>
                            <span class="text-gray-300 text-sm">London, UK</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i data-lucide="clock" class="w-5 h-5 text-blue-400"></i>
                            <span class="text-gray-300 text-sm">Mon-Fri: 9AM-6PM GMT</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    &copy; 2024 Deal4u. All rights reserved.
                </p>
                <div class="flex items-center space-x-4 mt-4 md:mt-0">
                    <span class="text-gray-400 text-sm">We accept:</span>
                    <div class="flex space-x-2">
                        <i data-lucide="credit-card" class="w-6 h-6 text-gray-400"></i>
                        <span class="text-gray-400 text-xs">Visa</span>
                        <span class="text-gray-400 text-xs">Mastercard</span>
                        <span class="text-gray-400 text-xs">PayPal</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Button -->
    <a href="https://wa.me/447447186806?text=Hello! I'm interested in your products."
       class="fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 z-40 hover:scale-110"
       id="whatsapp-btn">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
        </svg>
    </a>

    <!-- Back to Top Button -->
    <button
        id="back-to-top"
        class="fixed bottom-6 left-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40 hidden"
        onclick="scrollToTop()"
        aria-label="Back to top"
    >
        <i data-lucide="arrow-up" class="w-5 h-5"></i>
    </button>

    <!-- Product Quick View Modal -->
    <div id="product-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                    <h3 class="text-lg font-semibold">Product Details</h3>
                    <button onclick="closeProductModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
                <div id="modal-content" class="p-6">
                    <!-- Modal content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Cart Sidebar -->
    <div id="cart-sidebar" class="fixed inset-y-0 right-0 z-50 w-96 bg-white shadow-xl transform translate-x-full transition-transform duration-300">
        <div class="flex flex-col h-full">
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold">Shopping Cart</h3>
                <button onclick="toggleCart()" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <div id="cart-items" class="flex-1 overflow-y-auto p-4">
                <p class="text-gray-500 text-center py-8">Your cart is empty</p>
            </div>
            <div class="border-t border-gray-200 p-4">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-lg font-semibold">Total:</span>
                    <span class="text-lg font-bold text-blue-600">£<span id="cart-total">0.00</span></span>
                </div>
                <button onclick="proceedToCheckout()" class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    Proceed to Checkout
                </button>
            </div>
        </div>
    </div>

    <!-- Cart Overlay -->
    <div id="cart-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden" onclick="toggleCart()"></div>

    <!-- Scripts -->
    <script>
        // WooCommerce API Configuration
        const WOOCOMMERCE_CONFIG = {
            url: 'https://deal4u.co/wp-json/wc/v3',
            consumerKey: 'ck_b5f5e8f8c8f8c8f8c8f8c8f8c8f8c8f8c8f8c8f8',
            consumerSecret: 'cs_b5f5e8f8c8f8c8f8c8f8c8f8c8f8c8f8c8f8c8f8'
        };

        // Global variables
        let allProducts = [];
        let filteredProducts = [];
        let currentPage = 1;
        let productsPerPage = 24;
        let currentViewMode = 'grid';
        let currentFilters = {
            category: 'all',
            minPrice: '',
            maxPrice: '',
            rating: '',
            inStock: false,
            onSale: false,
            sort: 'default'
        };

        // Initialize Lucide icons
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            loadProducts();
            initializeEventListeners();
        });

        // Initialize event listeners
        function initializeEventListeners() {
            // Scroll event for back to top button
            window.addEventListener('scroll', function() {
                const backToTop = document.getElementById('back-to-top');
                if (window.pageYOffset > 300) {
                    backToTop.classList.remove('hidden');
                } else {
                    backToTop.classList.add('hidden');
                }
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(event) {
                const userDropdown = document.getElementById('user-dropdown');
                const userButton = event.target.closest('[onclick="toggleUserMenu()"]');

                if (!userButton && !userDropdown.contains(event.target)) {
                    userDropdown.classList.add('hidden');
                }
            });
        }

        // Load products from WooCommerce API
        async function loadProducts() {
            try {
                console.log('🚀 Loading products from WooCommerce API...');

                const response = await fetch(`${WOOCOMMERCE_CONFIG.url}/products?per_page=100&status=publish&consumer_key=${WOOCOMMERCE_CONFIG.consumerKey}&consumer_secret=${WOOCOMMERCE_CONFIG.consumerSecret}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const products = await response.json();
                console.log(`✅ Loaded ${products.length} products`);

                allProducts = products;
                filteredProducts = [...allProducts];

                // Load categories
                await loadCategories();

                // Display products
                displayProducts();
                updateProductCount();

                // Hide loading state
                document.getElementById('loading-state').classList.add('hidden');
                document.getElementById('products-content').classList.remove('hidden');

            } catch (error) {
                console.error('❌ Error loading products:', error);
                document.getElementById('loading-state').classList.add('hidden');
                document.getElementById('no-products-state').classList.remove('hidden');
            }
        }

        // Load categories from WooCommerce API
        async function loadCategories() {
            try {
                const response = await fetch(`${WOOCOMMERCE_CONFIG.url}/products/categories?per_page=100&consumer_key=${WOOCOMMERCE_CONFIG.consumerKey}&consumer_secret=${WOOCOMMERCE_CONFIG.consumerSecret}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const categories = await response.json();
                console.log(`✅ Loaded ${categories.length} categories`);

                // Populate category filters
                const categoryFilters = document.getElementById('category-filters');
                categoryFilters.innerHTML = '';

                categories.forEach(category => {
                    if (category.count > 0) { // Only show categories with products
                        const label = document.createElement('label');
                        label.className = 'flex items-center';
                        label.innerHTML = `
                            <input
                                type="radio"
                                name="category"
                                value="${category.slug}"
                                onchange="handleCategoryFilter(this.value)"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <span class="ml-3 text-sm text-gray-700">
                                ${category.name}
                                <span class="text-gray-500 ml-1">(${category.count})</span>
                            </span>
                        `;
                        categoryFilters.appendChild(label);
                    }
                });

            } catch (error) {
                console.error('❌ Error loading categories:', error);
            }
        }

        // Display products in grid or list view
        function displayProducts() {
            const productsGrid = document.getElementById('products-grid');
            const startIndex = (currentPage - 1) * productsPerPage;
            const endIndex = startIndex + productsPerPage;
            const currentProducts = filteredProducts.slice(startIndex, endIndex);

            if (currentViewMode === 'grid') {
                productsGrid.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
            } else {
                productsGrid.className = 'space-y-6';
            }

            productsGrid.innerHTML = '';

            currentProducts.forEach(product => {
                const productCard = createProductCard(product, currentViewMode);
                productsGrid.appendChild(productCard);
            });

            // Update pagination
            updatePagination();

            // Reinitialize Lucide icons
            lucide.createIcons();
        }

        // Create product card element
        function createProductCard(product, viewMode) {
            const card = document.createElement('div');

            const imageUrl = product.images && product.images.length > 0
                ? product.images[0].src
                : 'https://via.placeholder.com/300x300?text=No+Image';

            const price = parseFloat(product.price) || 0;
            const regularPrice = parseFloat(product.regular_price) || price;
            const salePrice = parseFloat(product.sale_price) || 0;
            const isOnSale = salePrice > 0 && salePrice < regularPrice;

            const rating = parseFloat(product.average_rating) || 0;
            const ratingCount = parseInt(product.rating_count) || 0;

            if (viewMode === 'grid') {
                card.className = 'bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group';
                card.innerHTML = `
                    <div class="relative">
                        <img src="${imageUrl}" alt="${product.name}" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        ${isOnSale ? '<div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">SALE</div>' : ''}
                        <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <button onclick="addToWishlist(${product.id})" class="bg-white p-2 rounded-full shadow-md hover:bg-red-50 transition-colors">
                                <i data-lucide="heart" class="w-4 h-4 text-gray-600 hover:text-red-500"></i>
                            </button>
                        </div>
                        <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <button onclick="quickView(${product.id})" class="bg-blue-600 text-white p-2 rounded-full shadow-md hover:bg-blue-700 transition-colors">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">${product.name}</h3>
                        <div class="flex items-center mb-2">
                            ${generateStarRating(rating)}
                            <span class="text-sm text-gray-500 ml-2">(${ratingCount})</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                ${isOnSale ? `
                                    <span class="text-lg font-bold text-red-600">£${salePrice.toFixed(2)}</span>
                                    <span class="text-sm text-gray-500 line-through">£${regularPrice.toFixed(2)}</span>
                                ` : `
                                    <span class="text-lg font-bold text-gray-900">£${price.toFixed(2)}</span>
                                `}
                            </div>
                            <button onclick="addToCart(${product.id})" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                `;
            } else {
                // List view
                card.className = 'bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden';
                card.innerHTML = `
                    <div class="flex">
                        <div class="relative w-48 h-48">
                            <img src="${imageUrl}" alt="${product.name}" class="w-full h-full object-cover">
                            ${isOnSale ? '<div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">SALE</div>' : ''}
                        </div>
                        <div class="flex-1 p-6">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h3 class="text-xl font-semibold text-gray-900 mb-2">${product.name}</h3>
                                    <div class="flex items-center mb-3">
                                        ${generateStarRating(rating)}
                                        <span class="text-sm text-gray-500 ml-2">(${ratingCount} reviews)</span>
                                    </div>
                                    <div class="text-gray-600 mb-4 line-clamp-3">${product.short_description || 'No description available.'}</div>
                                    <div class="flex items-center space-x-4">
                                        <div class="flex items-center space-x-2">
                                            ${isOnSale ? `
                                                <span class="text-2xl font-bold text-red-600">£${salePrice.toFixed(2)}</span>
                                                <span class="text-lg text-gray-500 line-through">£${regularPrice.toFixed(2)}</span>
                                            ` : `
                                                <span class="text-2xl font-bold text-gray-900">£${price.toFixed(2)}</span>
                                            `}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex flex-col space-y-2 ml-6">
                                    <button onclick="addToCart(${product.id})" class="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                                        Add to Cart
                                    </button>
                                    <button onclick="addToWishlist(${product.id})" class="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                                        <i data-lucide="heart" class="w-4 h-4 inline mr-2"></i>
                                        Wishlist
                                    </button>
                                    <button onclick="quickView(${product.id})" class="border border-blue-600 text-blue-600 px-6 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors">
                                        Quick View
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            return card;
        }

        // Generate star rating HTML
        function generateStarRating(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 !== 0;
            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

            let starsHTML = '';

            // Full stars
            for (let i = 0; i < fullStars; i++) {
                starsHTML += '<i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>';
            }

            // Half star
            if (hasHalfStar) {
                starsHTML += '<i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current opacity-50"></i>';
            }

            // Empty stars
            for (let i = 0; i < emptyStars; i++) {
                starsHTML += '<i data-lucide="star" class="w-4 h-4 text-gray-300"></i>';
            }

            return `<div class="flex">${starsHTML}</div>`;
        }

        // Update product count display
        function updateProductCount() {
            const startIndex = (currentPage - 1) * productsPerPage;
            const endIndex = Math.min(startIndex + productsPerPage, filteredProducts.length);
            const productCount = document.getElementById('product-count');

            if (filteredProducts.length === 0) {
                productCount.textContent = 'No products found';
            } else {
                productCount.textContent = `Showing ${startIndex + 1}-${endIndex} of ${filteredProducts.length} products`;
            }
        }

        // Update pagination
        function updatePagination() {
            const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.classList.add('hidden');
                return;
            }

            pagination.classList.remove('hidden');
            pagination.innerHTML = '';

            // Previous button
            const prevBtn = document.createElement('button');
            prevBtn.onclick = () => changePage(currentPage - 1);
            prevBtn.disabled = currentPage === 1;
            prevBtn.className = 'p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50';
            prevBtn.innerHTML = '<i data-lucide="chevron-left" class="w-5 h-5"></i>';
            pagination.appendChild(prevBtn);

            // Page numbers
            const pageNumbers = document.createElement('div');
            pageNumbers.className = 'flex space-x-1';

            for (let i = 1; i <= totalPages; i++) {
                const showPage = i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1);

                if (!showPage) {
                    if (i === currentPage - 2 || i === currentPage + 2) {
                        const ellipsis = document.createElement('span');
                        ellipsis.className = 'px-3 py-2 text-gray-500';
                        ellipsis.textContent = '...';
                        pageNumbers.appendChild(ellipsis);
                    }
                    continue;
                }

                const pageBtn = document.createElement('button');
                pageBtn.onclick = () => changePage(i);
                pageBtn.className = `px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    i === currentPage
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100 border border-gray-300'
                }`;
                pageBtn.textContent = i;
                pageNumbers.appendChild(pageBtn);
            }

            pagination.appendChild(pageNumbers);

            // Next button
            const nextBtn = document.createElement('button');
            nextBtn.onclick = () => changePage(currentPage + 1);
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.className = 'p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50';
            nextBtn.innerHTML = '<i data-lucide="chevron-right" class="w-5 h-5"></i>';
            pagination.appendChild(nextBtn);

            // Reinitialize Lucide icons
            lucide.createIcons();
        }

        // Change page
        function changePage(page) {
            const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                displayProducts();
                updateProductCount();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        // Set view mode
        function setViewMode(mode) {
            currentViewMode = mode;

            const gridBtn = document.getElementById('grid-view-btn');
            const listBtn = document.getElementById('list-view-btn');

            if (mode === 'grid') {
                gridBtn.className = 'p-2 rounded-md transition-colors bg-blue-600 text-white';
                listBtn.className = 'p-2 rounded-md transition-colors text-gray-600 hover:bg-gray-100';
            } else {
                gridBtn.className = 'p-2 rounded-md transition-colors text-gray-600 hover:bg-gray-100';
                listBtn.className = 'p-2 rounded-md transition-colors bg-blue-600 text-white';
            }

            displayProducts();
        }

        // Handle sort change
        function handleSortChange() {
            const sortSelect = document.getElementById('sort-select');
            currentFilters.sort = sortSelect.value;
            applyFilters();
        }

        // Handle category filter
        function handleCategoryFilter(category) {
            currentFilters.category = category;
            applyFilters();
        }

        // Handle price range
        function handlePriceRange(min, max) {
            currentFilters.minPrice = min.toString();
            currentFilters.maxPrice = max.toString();

            // Update custom price inputs
            document.getElementById('min-price').value = min;
            document.getElementById('max-price').value = max === '' ? '' : max;

            applyFilters();
        }

        // Handle custom price range
        function handleCustomPriceRange() {
            const minPrice = document.getElementById('min-price').value;
            const maxPrice = document.getElementById('max-price').value;

            currentFilters.minPrice = minPrice;
            currentFilters.maxPrice = maxPrice;

            applyFilters();
        }

        // Handle rating filter
        function handleRatingFilter(rating) {
            currentFilters.rating = rating;
            applyFilters();
        }

        // Handle availability filter
        function handleAvailabilityFilter() {
            const inStockFilter = document.getElementById('in-stock-filter');
            const onSaleFilter = document.getElementById('on-sale-filter');

            currentFilters.inStock = inStockFilter.checked;
            currentFilters.onSale = onSaleFilter.checked;

            applyFilters();
        }

        // Apply all filters
        function applyFilters() {
            filteredProducts = allProducts.filter(product => {
                // Category filter
                if (currentFilters.category !== 'all') {
                    const productCategories = product.categories.map(cat => cat.slug);
                    if (!productCategories.includes(currentFilters.category)) {
                        return false;
                    }
                }

                // Price filter
                const price = parseFloat(product.price) || 0;
                if (currentFilters.minPrice && price < parseFloat(currentFilters.minPrice)) {
                    return false;
                }
                if (currentFilters.maxPrice && price > parseFloat(currentFilters.maxPrice)) {
                    return false;
                }

                // Rating filter
                if (currentFilters.rating) {
                    const rating = parseFloat(product.average_rating) || 0;
                    if (rating < parseFloat(currentFilters.rating)) {
                        return false;
                    }
                }

                // In stock filter
                if (currentFilters.inStock && !product.in_stock) {
                    return false;
                }

                // On sale filter
                if (currentFilters.onSale && !product.on_sale) {
                    return false;
                }

                return true;
            });

            // Apply sorting
            applySorting();

            // Reset to first page
            currentPage = 1;

            // Update display
            displayProducts();
            updateProductCount();
            updateActiveFiltersIndicator();
        }

        // Apply sorting
        function applySorting() {
            switch (currentFilters.sort) {
                case 'price-low':
                    filteredProducts.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
                    break;
                case 'price-high':
                    filteredProducts.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
                    break;
                case 'rating':
                    filteredProducts.sort((a, b) => parseFloat(b.average_rating) - parseFloat(a.average_rating));
                    break;
                case 'popularity':
                    filteredProducts.sort((a, b) => parseInt(b.rating_count) - parseInt(a.rating_count));
                    break;
                case 'date':
                    filteredProducts.sort((a, b) => new Date(b.date_created) - new Date(a.date_created));
                    break;
                default:
                    // Keep original order
                    break;
            }
        }

        // Update active filters indicator
        function updateActiveFiltersIndicator() {
            const hasActiveFilters = currentFilters.category !== 'all' ||
                                   currentFilters.minPrice ||
                                   currentFilters.maxPrice ||
                                   currentFilters.rating ||
                                   currentFilters.inStock ||
                                   currentFilters.onSale;

            const badge = document.getElementById('active-filters-badge');
            const clearBtn = document.getElementById('clear-filters-btn');

            if (hasActiveFilters) {
                badge.classList.remove('hidden');
                clearBtn.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
                clearBtn.classList.add('hidden');
            }
        }

        // Clear all filters
        function clearAllFilters() {
            currentFilters = {
                category: 'all',
                minPrice: '',
                maxPrice: '',
                rating: '',
                inStock: false,
                onSale: false,
                sort: 'default'
            };

            // Reset form elements
            document.querySelector('input[name="category"][value="all"]').checked = true;
            document.getElementById('min-price').value = '';
            document.getElementById('max-price').value = '';
            document.querySelector('input[name="rating"][value=""]').checked = true;
            document.getElementById('in-stock-filter').checked = false;
            document.getElementById('on-sale-filter').checked = false;
            document.getElementById('sort-select').value = 'default';

            applyFilters();
        }

        // Toggle filter sections
        function toggleFilterSection(section) {
            const sectionElement = document.getElementById(`${section}-section`);
            const chevron = document.getElementById(`${section}-chevron`);

            if (sectionElement.classList.contains('hidden')) {
                sectionElement.classList.remove('hidden');
                chevron.setAttribute('data-lucide', 'chevron-up');
            } else {
                sectionElement.classList.add('hidden');
                chevron.setAttribute('data-lucide', 'chevron-down');
            }

            lucide.createIcons();
        }

        // Toggle mobile filters
        function toggleFilters() {
            const filterPanel = document.getElementById('filter-panel');
            const chevron = document.getElementById('filter-chevron');

            if (filterPanel.classList.contains('hidden')) {
                filterPanel.classList.remove('hidden');
                chevron.setAttribute('data-lucide', 'chevron-up');
            } else {
                filterPanel.classList.add('hidden');
                chevron.setAttribute('data-lucide', 'chevron-down');
            }

            lucide.createIcons();
        }

        // Search functionality
        function handleSearchSubmit(event) {
            event.preventDefault();
            const searchInput = event.target.querySelector('input[type="text"]');
            const query = searchInput.value.trim();
            if (query) {
                // Filter products by search query
                filteredProducts = allProducts.filter(product =>
                    product.name.toLowerCase().includes(query.toLowerCase()) ||
                    (product.description && product.description.toLowerCase().includes(query.toLowerCase())) ||
                    (product.short_description && product.short_description.toLowerCase().includes(query.toLowerCase()))
                );
                currentPage = 1;
                displayProducts();
                updateProductCount();
            }
        }

        function handleSearchInput(input) {
            const submitBtn = document.getElementById('search-submit-btn');
            const mobileSubmitBtn = document.getElementById('mobile-search-submit-btn');

            if (input.value.trim()) {
                if (submitBtn) submitBtn.classList.remove('hidden');
                if (mobileSubmitBtn) mobileSubmitBtn.classList.remove('hidden');
            } else {
                if (submitBtn) submitBtn.classList.add('hidden');
                if (mobileSubmitBtn) mobileSubmitBtn.classList.add('hidden');
            }
        }

        // Image search functionality
        function openImageSearch() {
            document.getElementById('image-search-input').click();
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                console.log('Image uploaded for search:', file.name);
                // Here you would implement image search functionality
                alert('Image search functionality would be implemented here');
            }
        }

        // Mobile menu toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        }

        // User menu toggle
        function toggleUserMenu() {
            const userDropdown = document.getElementById('user-dropdown');
            userDropdown.classList.toggle('hidden');
        }

        // Cart functionality
        function toggleCart() {
            const cartSidebar = document.getElementById('cart-sidebar');
            const cartOverlay = document.getElementById('cart-overlay');

            if (cartSidebar.classList.contains('translate-x-full')) {
                cartSidebar.classList.remove('translate-x-full');
                cartOverlay.classList.remove('hidden');
            } else {
                cartSidebar.classList.add('translate-x-full');
                cartOverlay.classList.add('hidden');
            }
        }

        function addToCart(productId) {
            console.log('Adding product to cart:', productId);
            // Here you would implement cart functionality
            alert('Product added to cart!');
        }

        function addToWishlist(productId) {
            console.log('Adding product to wishlist:', productId);
            // Here you would implement wishlist functionality
            alert('Product added to wishlist!');
        }

        function quickView(productId) {
            console.log('Quick view for product:', productId);
            // Here you would implement quick view functionality
            const product = allProducts.find(p => p.id === productId);
            if (product) {
                showProductModal(product);
            }
        }

        function showProductModal(product) {
            const modal = document.getElementById('product-modal');
            const modalContent = document.getElementById('modal-content');

            const imageUrl = product.images && product.images.length > 0
                ? product.images[0].src
                : 'https://via.placeholder.com/400x400?text=No+Image';

            modalContent.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <img src="${imageUrl}" alt="${product.name}" class="w-full rounded-lg">
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">${product.name}</h2>
                        <div class="flex items-center mb-4">
                            ${generateStarRating(parseFloat(product.average_rating) || 0)}
                            <span class="text-sm text-gray-500 ml-2">(${product.rating_count || 0} reviews)</span>
                        </div>
                        <div class="text-3xl font-bold text-blue-600 mb-4">£${parseFloat(product.price).toFixed(2)}</div>
                        <div class="text-gray-600 mb-6">${product.short_description || 'No description available.'}</div>
                        <div class="flex space-x-4">
                            <button onclick="addToCart(${product.id})" class="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                                Add to Cart
                            </button>
                            <button onclick="addToWishlist(${product.id})" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                                <i data-lucide="heart" class="w-5 h-5"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            modal.classList.remove('hidden');
            lucide.createIcons();
        }

        function closeProductModal() {
            document.getElementById('product-modal').classList.add('hidden');
        }

        function proceedToCheckout() {
            window.location.href = 'checkout.html';
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Close modal when clicking outside
        document.getElementById('product-modal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeProductModal();
            }
        });
    </script>
</body>
</html>
</body>
</html>
