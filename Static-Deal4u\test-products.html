<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Products - Deal4u.co</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 2rem;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .product-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .product-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);
        }

        .product-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .product-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .product-price {
            color: #e53e3e;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .product-status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .status-publish { background: #d4edda; color: #155724; }
        .status-draft { background: #fff3cd; color: #856404; }
        .status-private { background: #f8d7da; color: #721c24; }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #667eea;
        }

        .error {
            text-align: center;
            padding: 2rem;
            background: #f8d7da;
            color: #721c24;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .controls {
            text-align: center;
            margin-bottom: 2rem;
        }

        .stats {
            background: #e6fffa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛍️ WooCommerce Products Test</h1>
        
        <div class="controls">
            <button class="btn" onclick="loadProducts('publish')">Published Products</button>
            <button class="btn" onclick="loadProducts('draft')">Draft Products</button>
            <button class="btn" onclick="loadProducts('private')">Private Products</button>
            <button class="btn" onclick="loadProducts('any')">All Products</button>
        </div>

        <div id="stats" class="stats" style="display: none;"></div>
        <div id="products-container">
            <div class="loading">
                <h3>🔄 Loading products...</h3>
                <p>Testing WooCommerce API connection...</p>
            </div>
        </div>
    </div>

    <script>
        // API Configuration
        const API_CONFIG = {
            baseURL: 'https://deal4u.co',
            consumerKey: 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193',
            consumerSecret: 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3'
        };

        let allProducts = [];

        // Load products with different statuses
        async function loadProducts(status = 'any') {
            const container = document.getElementById('products-container');
            const statsDiv = document.getElementById('stats');
            
            container.innerHTML = `
                <div class="loading">
                    <h3>🔄 Loading ${status} products...</h3>
                    <p>Please wait...</p>
                </div>
            `;

            try {
                const url = `${API_CONFIG.baseURL}/wp-json/wc/v3/products?per_page=100&status=${status}&consumer_key=${API_CONFIG.consumerKey}&consumer_secret=${API_CONFIG.consumerSecret}`;
                
                console.log('🌐 API Request:', url);
                
                const response = await fetch(url);
                const products = await response.json();

                if (response.ok) {
                    allProducts = products;
                    displayProducts(products);
                    showStats(products, status);
                } else {
                    throw new Error(`API Error: ${response.status} - ${JSON.stringify(products)}`);
                }
            } catch (error) {
                console.error('❌ Error loading products:', error);
                container.innerHTML = `
                    <div class="error">
                        <h3>❌ Failed to load products</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p><strong>Status:</strong> ${status}</p>
                        <p><strong>API URL:</strong> ${API_CONFIG.baseURL}</p>
                    </div>
                `;
                statsDiv.style.display = 'none';
            }
        }

        // Display products
        function displayProducts(products) {
            const container = document.getElementById('products-container');
            
            if (products.length === 0) {
                container.innerHTML = `
                    <div class="error">
                        <h3>📦 No products found</h3>
                        <p>No products match the selected criteria.</p>
                    </div>
                `;
                return;
            }

            const productCards = products.map(product => {
                const imageUrl = product.images?.[0]?.src || 'https://via.placeholder.com/200x200?text=No+Image';
                const statusClass = `status-${product.status}`;
                
                return `
                    <div class="product-card" onclick="openProduct(${product.id})">
                        <img src="${imageUrl}" alt="${product.name}" class="product-image" onerror="this.src='https://via.placeholder.com/200x200?text=No+Image'">
                        <div class="product-title">${product.name}</div>
                        <div class="product-price">£${product.price || '0.00'}</div>
                        <div class="product-status ${statusClass}">${product.status.toUpperCase()}</div>
                        <div style="font-size: 0.8rem; color: #666; margin-top: 0.5rem;">ID: ${product.id}</div>
                    </div>
                `;
            }).join('');

            container.innerHTML = `<div class="product-grid">${productCards}</div>`;
        }

        // Show statistics
        function showStats(products, status) {
            const statsDiv = document.getElementById('stats');
            const totalProducts = products.length;
            const publishedCount = products.filter(p => p.status === 'publish').length;
            const draftCount = products.filter(p => p.status === 'draft').length;
            const privateCount = products.filter(p => p.status === 'private').length;

            statsDiv.innerHTML = `
                <h3>📊 Products Statistics (${status})</h3>
                <p><strong>Total Found:</strong> ${totalProducts} products</p>
                <p><strong>Published:</strong> ${publishedCount} | <strong>Draft:</strong> ${draftCount} | <strong>Private:</strong> ${privateCount}</p>
            `;
            statsDiv.style.display = 'block';
        }

        // Open product page
        function openProduct(productId) {
            window.open(`product.html?id=${productId}`, '_blank');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts('any');
        });
    </script>
</body>
</html>
