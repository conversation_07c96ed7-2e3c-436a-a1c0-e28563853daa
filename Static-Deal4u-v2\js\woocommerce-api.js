// Mock Data for Demo
const MOCK_PRODUCTS = [
    {
        id: 1,
        name: "Premium Wireless Headphones",
        price: "89.99",
        regular_price: "129.99",
        description: "High-quality wireless headphones with noise cancellation",
        short_description: "Premium wireless headphones with excellent sound quality",
        images: [{ src: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400" }],
        status: "publish",
        featured: true,
        on_sale: true,
        stock_status: "instock"
    },
    {
        id: 2,
        name: "Elegant Summer Dress",
        price: "45.99",
        regular_price: "65.99",
        description: "Beautiful summer dress perfect for any occasion",
        short_description: "Elegant dress for women, perfect for summer",
        images: [{ src: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400" }],
        status: "publish",
        featured: true,
        on_sale: true,
        stock_status: "instock"
    },
    {
        id: 3,
        name: "Luxury Watch Collection",
        price: "199.99",
        regular_price: "299.99",
        description: "Premium luxury watch with leather strap",
        short_description: "Elegant luxury watch for special occasions",
        images: [{ src: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400" }],
        status: "publish",
        featured: false,
        on_sale: true,
        stock_status: "instock"
    },
    {
        id: 4,
        name: "Smart Phone Pro Max",
        price: "899.99",
        regular_price: "999.99",
        description: "Latest smartphone with advanced features",
        short_description: "High-end smartphone with amazing camera",
        images: [{ src: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400" }],
        status: "publish",
        featured: true,
        on_sale: false,
        stock_status: "instock"
    },
    {
        id: 5,
        name: "Designer Handbag",
        price: "129.99",
        regular_price: "179.99",
        description: "Stylish designer handbag for everyday use",
        short_description: "Premium leather handbag with elegant design",
        images: [{ src: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400" }],
        status: "publish",
        featured: false,
        on_sale: true,
        stock_status: "instock"
    },
    {
        id: 6,
        name: "Gaming Laptop Ultra",
        price: "1299.99",
        regular_price: "1499.99",
        description: "High-performance gaming laptop",
        short_description: "Powerful laptop for gaming and work",
        images: [{ src: "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400" }],
        status: "publish",
        featured: true,
        on_sale: true,
        stock_status: "instock"
    },
    {
        id: 7,
        name: "Casual Women's Jeans",
        price: "39.99",
        regular_price: "59.99",
        description: "Comfortable casual jeans for everyday wear",
        short_description: "Stylish women's jeans with perfect fit",
        images: [{ src: "https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=400" }],
        status: "publish",
        featured: false,
        on_sale: true,
        stock_status: "instock"
    },
    {
        id: 8,
        name: "Bluetooth Speaker",
        price: "49.99",
        regular_price: "79.99",
        description: "Portable Bluetooth speaker with great sound",
        short_description: "Compact speaker with powerful bass",
        images: [{ src: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400" }],
        status: "publish",
        featured: false,
        on_sale: true,
        stock_status: "instock"
    },
    {
        id: 9,
        name: "Sunglasses Collection",
        price: "79.99",
        regular_price: "119.99",
        description: "Stylish sunglasses with UV protection",
        short_description: "Premium sunglasses for fashion and protection",
        images: [{ src: "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400" }],
        status: "publish",
        featured: false,
        on_sale: true,
        stock_status: "instock"
    },
    {
        id: 10,
        name: "Fitness Tracker Pro",
        price: "159.99",
        regular_price: "199.99",
        description: "Advanced fitness tracker with health monitoring",
        short_description: "Smart fitness tracker for health enthusiasts",
        images: [{ src: "https://images.unsplash.com/photo-**********-31a4b719223d?w=400" }],
        status: "publish",
        featured: true,
        on_sale: true,
        stock_status: "instock"
    }
];

// WooCommerce API Handler
class WooCommerceAPI {
    constructor() {
        this.baseUrl = CONFIG.WOOCOMMERCE.BASE_URL;
        this.consumerKey = CONFIG.WOOCOMMERCE.CONSUMER_KEY;
        this.consumerSecret = CONFIG.WOOCOMMERCE.CONSUMER_SECRET;
        this.useProxy = CONFIG.WOOCOMMERCE.USE_PROXY;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    // Generate OAuth signature for WooCommerce API
    generateAuthHeader() {
        if (this.useProxy) {
            return {};
        }

        const credentials = btoa(`${this.consumerKey}:${this.consumerSecret}`);
        return {
            'Authorization': `Basic ${credentials}`,
            'Content-Type': 'application/json'
        };
    }

    // Make API request with error handling and caching
    async makeRequest(endpoint, options = {}) {
        const cacheKey = `${endpoint}_${JSON.stringify(options)}`;
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                if (CONFIG.DEBUG.LOG_API_CALLS) {
                    console.log('Cache hit for:', endpoint);
                }
                return cached.data;
            }
        }

        try {
            const url = this.useProxy ? 
                `${CONFIG.WOOCOMMERCE.PROXY_URL}${endpoint}` : 
                `${this.baseUrl}${endpoint}`;

            const headers = this.generateAuthHeader();
            
            const response = await fetch(url, {
                method: options.method || 'GET',
                headers: {
                    ...headers,
                    ...options.headers
                },
                body: options.body ? JSON.stringify(options.body) : undefined
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            
            // Cache successful GET requests
            if (!options.method || options.method === 'GET') {
                this.cache.set(cacheKey, {
                    data,
                    timestamp: Date.now()
                });
            }

            if (CONFIG.DEBUG.LOG_API_CALLS) {
                console.log('API Response for', endpoint, ':', data);
            }

            return data;
        } catch (error) {
            console.error('WooCommerce API Error:', error);
            
            // Return cached data if available, even if expired
            if (this.cache.has(cacheKey)) {
                console.warn('Using expired cache data due to API error');
                return this.cache.get(cacheKey).data;
            }
            
            throw error;
        }
    }

    // Get products with filtering and pagination
    async getProducts(params = {}) {
        // Use mock data if enabled
        if (CONFIG.DEBUG.USE_MOCK_DATA) {
            console.log('Using mock data for products');
            return new Promise(resolve => {
                setTimeout(() => {
                    let products = [...MOCK_PRODUCTS];

                    // Apply filters
                    if (params.status && params.status !== 'any') {
                        products = products.filter(p => p.status === params.status);
                    }

                    if (params.featured) {
                        products = products.filter(p => p.featured);
                    }

                    if (params.on_sale) {
                        products = products.filter(p => p.on_sale);
                    }

                    if (params.search) {
                        products = products.filter(p =>
                            p.name.toLowerCase().includes(params.search.toLowerCase())
                        );
                    }

                    // Apply pagination
                    const perPage = params.per_page || CONFIG.PRODUCTS.PER_PAGE;
                    products = products.slice(0, perPage);

                    resolve(products);
                }, 500); // Simulate network delay
            });
        }

        const defaultParams = {
            per_page: CONFIG.PRODUCTS.PER_PAGE,
            status: 'publish',
            stock_status: 'instock'
        };

        const queryParams = { ...defaultParams, ...params };
        const queryString = new URLSearchParams(queryParams).toString();

        return await this.makeRequest(`/products?${queryString}`);
    }

    // Get all products (including draft/private for admin)
    async getAllProducts(includeAll = false) {
        // Use mock data if enabled
        if (CONFIG.DEBUG.USE_MOCK_DATA) {
            console.log('Using mock data for all products');
            return new Promise(resolve => {
                setTimeout(() => {
                    let products = [...MOCK_PRODUCTS];

                    if (!includeAll) {
                        products = products.filter(p => p.status === 'publish');
                    }

                    resolve(products);
                }, 300);
            });
        }

        const params = {
            per_page: CONFIG.PRODUCTS.MAX_PRODUCTS,
            status: includeAll ? 'any' : 'publish'
        };

        return await this.getProducts(params);
    }

    // Get single product by ID
    async getProduct(id) {
        return await this.makeRequest(`/products/${id}`);
    }

    // Get product categories
    async getCategories() {
        return await this.makeRequest('/products/categories?per_page=100');
    }

    // Search products
    async searchProducts(query, limit = 20) {
        const params = {
            search: query,
            per_page: limit,
            status: 'publish'
        };

        return await this.getProducts(params);
    }

    // Get products by category
    async getProductsByCategory(categoryId, limit = 20) {
        const params = {
            category: categoryId,
            per_page: limit,
            status: 'publish'
        };

        return await this.getProducts(params);
    }

    // Get featured products
    async getFeaturedProducts(limit = 20) {
        const params = {
            featured: true,
            per_page: limit,
            status: 'publish'
        };

        return await this.getProducts(params);
    }

    // Get products on sale
    async getSaleProducts(limit = 20) {
        const params = {
            on_sale: true,
            per_page: limit,
            status: 'publish'
        };

        return await this.getProducts(params);
    }

    // Create order (for checkout)
    async createOrder(orderData) {
        return await this.makeRequest('/orders', {
            method: 'POST',
            body: orderData
        });
    }

    // Get order by ID
    async getOrder(id) {
        return await this.makeRequest(`/orders/${id}`);
    }

    // Create customer
    async createCustomer(customerData) {
        return await this.makeRequest('/customers', {
            method: 'POST',
            body: customerData
        });
    }

    // Get customer by email
    async getCustomerByEmail(email) {
        return await this.makeRequest(`/customers?email=${encodeURIComponent(email)}`);
    }

    // Apply coupon
    async getCoupon(code) {
        return await this.makeRequest(`/coupons?code=${encodeURIComponent(code)}`);
    }

    // Clear cache
    clearCache() {
        this.cache.clear();
        console.log('API cache cleared');
    }

    // Get cache stats
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}

// Create global instance
const wooAPI = new WooCommerceAPI();

// Helper functions for common operations
async function loadProducts(category = null, limit = null) {
    try {
        let products;
        
        if (category && category !== 'all') {
            // For smart categorization, we'll filter client-side
            products = await wooAPI.getAllProducts();
            products = products.filter(product => {
                const productCategory = getCategoryByKeywords(
                    product.name, 
                    product.short_description || product.description || ''
                );
                return productCategory === category;
            });
        } else {
            products = await wooAPI.getAllProducts();
        }

        if (limit) {
            products = products.slice(0, limit);
        }

        return products;
    } catch (error) {
        console.error('Error loading products:', error);
        return [];
    }
}

async function searchProductsGlobal(query) {
    try {
        return await wooAPI.searchProducts(query);
    } catch (error) {
        console.error('Error searching products:', error);
        return [];
    }
}

async function getProductDetails(id) {
    try {
        return await wooAPI.getProduct(id);
    } catch (error) {
        console.error('Error getting product details:', error);
        return null;
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { WooCommerceAPI, wooAPI, loadProducts, searchProductsGlobal, getProductDetails };
}
