<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Deal4u</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#667eea">
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-nav-container">
            <a href="/" class="logo">
                <div class="logo-icon">D</div>
                <span>Deal4u Admin</span>
            </a>

            <nav class="admin-nav">
                <a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="#products" class="nav-link" data-section="products">
                    <i class="fas fa-box"></i> Products
                </a>
                <a href="#sync" class="nav-link" data-section="sync">
                    <i class="fas fa-sync"></i> Sync
                </a>
                <a href="#settings" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </nav>

            <div class="admin-actions">
                <button class="admin-btn" onclick="window.open('/', '_blank')">
                    <i class="fas fa-external-link-alt"></i> View Site
                </button>
                <button class="admin-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
    </header>

    <!-- Admin Content -->
    <main class="admin-main">
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="admin-section active">
            <div class="admin-container">
                <h1>Dashboard Overview</h1>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="total-products-count">0</div>
                            <div class="stat-label">Total Products</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="published-products-count">0</div>
                            <div class="stat-label">Published Products</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-draft2digital"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="draft-products-count">0</div>
                            <div class="stat-label">Draft Products</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="sale-products-count">0</div>
                            <div class="stat-label">On Sale</div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h3>Quick Actions</h3>
                        <div class="quick-actions">
                            <button class="action-btn" onclick="syncAllProducts()">
                                <i class="fas fa-sync"></i>
                                Sync All Products
                            </button>
                            <button class="action-btn" onclick="clearCache()">
                                <i class="fas fa-trash"></i>
                                Clear Cache
                            </button>
                            <button class="action-btn" onclick="exportData()">
                                <i class="fas fa-download"></i>
                                Export Data
                            </button>
                            <button class="action-btn" onclick="viewLogs()">
                                <i class="fas fa-file-alt"></i>
                                View Logs
                            </button>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <h3>System Status</h3>
                        <div class="status-list">
                            <div class="status-item">
                                <span>WooCommerce API</span>
                                <span class="status-badge" id="api-status">Checking...</span>
                            </div>
                            <div class="status-item">
                                <span>Cache Status</span>
                                <span class="status-badge" id="cache-status">Active</span>
                            </div>
                            <div class="status-item">
                                <span>Last Sync</span>
                                <span class="status-badge" id="last-sync">Never</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Section -->
        <section id="products-section" class="admin-section">
            <div class="admin-container">
                <div class="section-header">
                    <h1>Products Management</h1>
                    <button class="admin-btn primary" onclick="refreshProducts()">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>

                <div class="products-filters">
                    <select id="product-status-filter">
                        <option value="all">All Status</option>
                        <option value="publish">Published</option>
                        <option value="draft">Draft</option>
                        <option value="private">Private</option>
                    </select>
                    
                    <select id="product-category-filter">
                        <option value="all">All Categories</option>
                        <option value="woman-clothes">Woman Clothes</option>
                        <option value="electronics">Electronics</option>
                        <option value="accessories">Accessories</option>
                    </select>
                    
                    <input type="text" id="product-search" placeholder="Search products...">
                </div>

                <div class="products-table-container">
                    <table class="products-table">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Name</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Category</th>
                                <th>Stock</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="products-table-body">
                            <tr>
                                <td colspan="7" class="loading-row">
                                    <div class="loading">
                                        <div class="spinner"></div>
                                        <p>Loading products...</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Sync Section -->
        <section id="sync-section" class="admin-section">
            <div class="admin-container">
                <h1>Product Synchronization</h1>
                
                <div class="sync-options">
                    <div class="sync-card">
                        <h3>Full Sync</h3>
                        <p>Sync all products from WooCommerce including draft and private products.</p>
                        <button class="sync-btn" onclick="fullSync()">
                            <i class="fas fa-sync"></i>
                            Start Full Sync
                        </button>
                    </div>

                    <div class="sync-card">
                        <h3>Published Only</h3>
                        <p>Sync only published products that are visible to customers.</p>
                        <button class="sync-btn" onclick="publishedSync()">
                            <i class="fas fa-eye"></i>
                            Sync Published
                        </button>
                    </div>

                    <div class="sync-card">
                        <h3>Auto Sync</h3>
                        <p>Enable automatic synchronization when new products are imported.</p>
                        <button class="sync-btn" onclick="toggleAutoSync()" id="auto-sync-btn">
                            <i class="fas fa-robot"></i>
                            Enable Auto Sync
                        </button>
                    </div>
                </div>

                <div class="sync-log">
                    <h3>Sync Log</h3>
                    <div id="sync-log-content" class="log-content">
                        <p>No sync operations performed yet.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings-section" class="admin-section">
            <div class="admin-container">
                <h1>Settings</h1>
                
                <div class="settings-grid">
                    <div class="settings-card">
                        <h3>WooCommerce API</h3>
                        <form class="settings-form" onsubmit="saveApiSettings(event)">
                            <div class="form-group">
                                <label>Base URL</label>
                                <input type="url" id="api-base-url" placeholder="https://your-site.com/wp-json/wc/v3">
                            </div>
                            <div class="form-group">
                                <label>Consumer Key</label>
                                <input type="text" id="api-consumer-key" placeholder="ck_...">
                            </div>
                            <div class="form-group">
                                <label>Consumer Secret</label>
                                <input type="password" id="api-consumer-secret" placeholder="cs_...">
                            </div>
                            <button type="submit" class="settings-btn">Save API Settings</button>
                        </form>
                    </div>

                    <div class="settings-card">
                        <h3>Site Configuration</h3>
                        <form class="settings-form" onsubmit="saveSiteSettings(event)">
                            <div class="form-group">
                                <label>Site Name</label>
                                <input type="text" id="site-name" value="Deal4u">
                            </div>
                            <div class="form-group">
                                <label>Contact Email</label>
                                <input type="email" id="contact-email" value="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label>Phone Number</label>
                                <input type="tel" id="phone-number" value="+447447186806">
                            </div>
                            <button type="submit" class="settings-btn">Save Site Settings</button>
                        </form>
                    </div>

                    <div class="settings-card">
                        <h3>Cache Settings</h3>
                        <div class="cache-controls">
                            <button class="settings-btn" onclick="clearAllCache()">Clear All Cache</button>
                            <button class="settings-btn" onclick="preloadCache()">Preload Cache</button>
                            <div class="cache-info">
                                <p>Cache Size: <span id="cache-size">0 KB</span></p>
                                <p>Last Cleared: <span id="cache-cleared">Never</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/woocommerce-api.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/main.js"></script>

    <style>
        .admin-header {
            background: #1f2937;
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .admin-nav-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .admin-nav {
            display: flex;
            gap: 2rem;
        }

        .nav-link {
            color: #d1d5db;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-link:hover,
        .nav-link.active {
            background: #374151;
            color: white;
        }

        .admin-actions {
            display: flex;
            gap: 1rem;
        }

        .admin-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .admin-btn:hover {
            background: #5a67d8;
        }

        .admin-btn.primary {
            background: #10b981;
        }

        .admin-btn.primary:hover {
            background: #059669;
        }

        .admin-main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .admin-section {
            display: none;
        }

        .admin-section.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stat-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .dashboard-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 1rem;
        }

        .action-btn {
            background: #f3f4f6;
            border: 2px solid #e5e7eb;
            padding: 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            text-align: center;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .status-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1rem;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            background: #10b981;
            color: white;
        }

        .status-badge.error {
            background: #ef4444;
        }

        .status-badge.warning {
            background: #f59e0b;
        }

        @media (max-width: 768px) {
            .admin-nav {
                display: none;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
