/* Deal4u Static Website Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* Top Promotional Banner */
.promo-banner {
    background: linear-gradient(90deg, #ff6b35, #f7931e);
    color: white;
    text-align: center;
    padding: 8px 0;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.promo-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Summer Sale Banner */
.summer-banner {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    text-align: center;
    padding: 15px 0;
    font-weight: 700;
    font-size: 18px;
    position: relative;
    overflow: hidden;
}

.summer-banner .sale-badge {
    background: #dc2626;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    margin-right: 10px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.summer-banner .shop-now-btn {
    background: white;
    color: #f59e0b;
    padding: 8px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 600;
    margin-left: 15px;
    transition: all 0.3s;
}

.summer-banner .shop-now-btn:hover {
    background: #f3f4f6;
    transform: translateY(-2px);
}

/* Header */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2563eb;
    text-decoration: none;
}

.logo-icon {
    background: #2563eb;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
    position: relative;
}

.nav-menu a:hover {
    color: #2563eb;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #2563eb;
    transition: width 0.3s;
}

.nav-menu a:hover::after {
    width: 100%;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 8px 40px 8px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 25px;
    width: 250px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-box input:focus {
    outline: none;
    border-color: #2563eb;
}

.search-box button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #2563eb;
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-btn, .auth-btn {
    background: #2563eb;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: background 0.3s;
}

.cart-btn:hover, .auth-btn:hover {
    background: #1d4ed8;
}

.cart-count {
    background: #ff6b35;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="50" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="30" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content .highlight {
    color: #fbbf24;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn-primary {
    background: #fbbf24;
    color: #1f2937;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary:hover {
    background: #f59e0b;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    padding: 1rem 2rem;
    border: 2px solid white;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #fbbf24;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sale-badge-hero {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
    animation: pulse-hero 2s infinite;
    position: relative;
}

.sale-badge-hero::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: rotate 10s linear infinite;
}

@keyframes pulse-hero {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.sale-text {
    font-size: 1rem;
    margin-top: 0.5rem;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    width: 60px;
    height: 60px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    width: 40px;
    height: 40px;
    top: 60%;
    left: 80%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    width: 80px;
    height: 80px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Products Section */
.section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #1f2937;
}

.category-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 25px;
    background: white;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 500;
}

.filter-btn:hover, .filter-btn.active {
    background: #2563eb;
    color: white;
    border-color: #2563eb;
    transform: translateY(-2px);
}

.products-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 1.5rem !important;
    margin-top: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 1rem;
    justify-items: center;
    align-items: start;
}

@media (min-width: 576px) {
    .products-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1.5rem !important;
    }
}

@media (min-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 1.5rem !important;
    }
}

@media (min-width: 992px) {
    .products-grid {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 1.5rem !important;
    }
}

@media (min-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(5, 1fr) !important;
        gap: 1.5rem !important;
    }
}

.product-card {
    background: white !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
    height: 380px !important;
    width: 100% !important;
    max-width: 280px !important;
    cursor: pointer !important;
    border: 1px solid #e5e7eb !important;
    margin: 0 auto !important;
}

.product-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #667eea !important;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.product-card:hover::before {
    opacity: 1;
}

.product-image {
    width: 100% !important;
    height: 180px !important;
    position: relative !important;
    overflow: hidden !important;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0) !important;
}

.product-image img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: all 0.3s ease !important;
}

.product-card:hover .product-image img {
    transform: scale(1.1) rotate(2deg);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
    animation: pulse-badge 2s infinite;
    z-index: 5;
}

@keyframes pulse-badge {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.product-badge.hot {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.product-badge.new {
    background: linear-gradient(135deg, #10b981, #34d399);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.product-badge.limited {
    background: linear-gradient(135deg, #8b5cf6, #a78bfa);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
    animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4); }
    to { box-shadow: 0 4px 25px rgba(139, 92, 246, 0.8); }
}

.product-info {
    padding: 1rem !important;
    display: flex !important;
    flex-direction: column !important;
    flex-grow: 1 !important;
    justify-content: space-between !important;
    height: 200px !important;
}

.product-title {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    color: #1f2937 !important;
    line-height: 1.3 !important;
    transition: color 0.3s ease !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    height: 2.6rem !important;
}

.product-card:hover .product-title {
    color: #667eea;
}

.product-rating {
    display: flex !important;
    align-items: center !important;
    gap: 0.3rem !important;
    margin-bottom: 0.5rem !important;
}

.stars {
    color: #fbbf24 !important;
    font-size: 0.8rem !important;
    text-shadow: 0 1px 2px rgba(251, 191, 36, 0.3) !important;
}

.rating-text {
    color: #6b7280 !important;
    font-size: 0.7rem !important;
    font-weight: 500;
}

.product-price {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    margin-bottom: 0.8rem !important;
}

.current-price {
    font-size: 1.2rem !important;
    font-weight: 700 !important;
    color: #dc2626 !important;
    text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2) !important;
}

.original-price {
    font-size: 0.9rem !important;
    color: #9ca3af;
    text-decoration: line-through;
    font-weight: 500;
}

.savings {
    background: linear-gradient(135deg, #10b981, #34d399);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
}

.add-to-cart {
    width: 100% !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border: none !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 0.85rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.3px !important;
    position: relative !important;
    overflow: hidden !important;
    margin-top: auto !important;
}

.add-to-cart::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.add-to-cart:hover::before {
    left: 100%;
}

.add-to-cart:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.add-to-cart:active {
    transform: translateY(0);
}

.load-more-container {
    text-align: center;
    margin-top: 3rem;
}

/* Why Choose Deal4u Section */
.why-choose {
    background: #f8fafc;
    padding: 4rem 0;
}

.why-choose-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.why-choose h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
}

.why-choose p {
    font-size: 1.2rem;
    color: #6b7280;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.feature-description {
    color: #6b7280;
    line-height: 1.6;
}

/* Newsletter Section */
.newsletter {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.newsletter-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 2rem;
}

.newsletter h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.newsletter p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
}

.newsletter-form button {
    background: #fbbf24;
    color: #1f2937;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.newsletter-form button:hover {
    background: #f59e0b;
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #374151;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.social-links a:hover {
    background: #667eea;
    transform: translateY(-2px);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #9ca3af;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
}

/* WhatsApp Button */
.whatsapp-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: #25d366;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 32px;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.6);
    z-index: 1000;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(100px);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.whatsapp-btn.visible {
    opacity: 1;
    transform: translateY(0);
}

.whatsapp-btn:hover {
    transform: scale(1.15);
    box-shadow: 0 6px 25px rgba(37, 211, 102, 0.8);
    background: #128c7e;
}

.whatsapp-btn::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid rgba(37, 211, 102, 0.3);
    border-radius: 50%;
    animation: whatsapp-pulse 2s infinite;
}

@keyframes whatsapp-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.3);
        opacity: 0;
    }
}

.whatsapp-btn .whatsapp-icon {
    position: relative;
    z-index: 2;
}

/* Cart Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
}

.cart-sidebar.open {
    right: 0;
}

.cart-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

.close-cart {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #6b7280;
    transition: color 0.3s;
}

.close-cart:hover {
    color: #1f2937;
}

.cart-items {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.empty-cart {
    text-align: center;
    color: #6b7280;
    padding: 2rem;
}

.cart-footer {
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.cart-total {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    text-align: center;
}

.checkout-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
}

.checkout-btn:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
    transform: translateY(-2px);
}

/* Auth Modal */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.auth-modal.active {
    display: flex;
}

.auth-content {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    width: 90%;
    max-width: 400px;
    position: relative;
}

.auth-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #1f2937;
}

.auth-form input {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.auth-form input:focus {
    outline: none;
    border-color: #2563eb;
}

.auth-form button {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.auth-form button:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

.auth-form p {
    text-align: center;
    margin-top: 1rem;
    color: #6b7280;
}

.auth-form a {
    color: #2563eb;
    text-decoration: none;
}

.auth-form a:hover {
    text-decoration: underline;
}

/* Product Modal */
.product-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.product-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 25px;
    width: 95%;
    max-width: 1200px;
    height: 85vh;
    max-height: 700px;
    position: relative;
    transform: scale(0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.product-modal.active .modal-content {
    transform: scale(1);
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.1);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 20px;
    z-index: 10001;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
}

.modal-product-grid {
    display: grid;
    grid-template-columns: 1.4fr 0.6fr;
    height: 100%;
    overflow: hidden;
}

.modal-image-gallery {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.image-slider-container {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: white;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.modal-main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: zoom-in;
}

.modal-main-image:hover {
    transform: scale(1.05);
}

.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 20px;
    font-weight: bold;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 10;
}

.slider-nav:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
    transform: translateY(-50%) scale(1.1);
}

.slider-prev {
    left: 15px;
}

.slider-next {
    right: 15px;
}

.image-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.image-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s;
}

.image-dot:hover, .image-dot.active {
    background: white;
    transform: scale(1.2);
}

.image-counter {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.modal-product-info {
    padding: 2rem;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    overflow-y: auto;
}

.modal-product-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
    line-height: 1.3;
}

.modal-product-price {
    font-size: 2rem;
    font-weight: 900;
    background: linear-gradient(135deg, #dc2626, #ef4444);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.modal-product-description {
    color: #6b7280;
    line-height: 1.7;
    margin-bottom: 2rem;
    font-size: 1rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 15px;
    border-left: 4px solid #667eea;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.modal-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modal-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.modal-btn.primary:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.6);
}

.modal-btn.secondary {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    color: #374151;
    border: 2px solid #e5e7eb;
}

.modal-btn.secondary:hover {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    transform: translateY(-2px);
    border-color: #667eea;
}

/* Loading Spinner */
.loading {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #2563eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .search-box input {
        width: 150px;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .summer-banner {
        font-size: 16px;
        padding: 12px 0;
    }

    .summer-banner .shop-now-btn {
        display: block;
        margin: 10px auto 0;
        width: fit-content;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .cart-sidebar {
        width: 100% !important;
        right: -100% !important;
    }

    .modal-product-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .modal-main-image {
        height: 250px;
    }

    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .products-grid {
        grid-template-columns: 1fr !important;
    }

    .search-box {
        display: none;
    }

    .header-actions {
        gap: 0.5rem;
    }

    .cart-btn, .auth-btn {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.text-center {
    text-align: center;
}

.mt-4 {
    margin-top: 1rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.p-4 {
    padding: 1rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

/* Additional Tailwind-compatible utilities */
.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Star rating styles */
.text-yellow-400 {
    color: #fbbf24 !important;
}

/* Custom backdrop blur for older browsers */
@supports not (backdrop-filter: blur(4px)) {
    .backdrop-blur-sm {
        background-color: rgba(255, 255, 255, 0.8);
    }

    .backdrop-blur-md {
        background-color: rgba(255, 255, 255, 0.9);
    }
}
